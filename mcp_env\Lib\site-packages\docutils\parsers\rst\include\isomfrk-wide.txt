.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |Afr| unicode:: U+1D504 .. MATHEMATICAL FRAKTUR CAPITAL A
.. |afr| unicode:: U+1D51E .. MATHEMATICAL FRAKTUR SMALL A
.. |Bfr| unicode:: U+1D505 .. MATHEMATICAL FRAKTUR CAPITAL B
.. |bfr| unicode:: U+1D51F .. MATHEMATICAL FRAKTUR SMALL B
.. |Cfr| unicode:: U+0212D .. BLACK-LETTER CAPITAL C
.. |cfr| unicode:: U+1D520 .. MATHEMATICAL FRAKTUR SMALL C
.. |Dfr| unicode:: U+1D507 .. MATHEMATICAL FRAKTUR CAPITAL D
.. |dfr| unicode:: U+1D521 .. MATHEMATICAL FRAKTUR SMALL D
.. |Efr| unicode:: U+1D508 .. MATHEMATICAL FRAKTUR CAPITAL E
.. |efr| unicode:: U+1D522 .. MATHEMATICAL FRAKTUR SMALL E
.. |Ffr| unicode:: U+1D509 .. MATHEMATICAL FRAKTUR CAPITAL F
.. |ffr| unicode:: U+1D523 .. MATHEMATICAL FRAKTUR SMALL F
.. |Gfr| unicode:: U+1D50A .. MATHEMATICAL FRAKTUR CAPITAL G
.. |gfr| unicode:: U+1D524 .. MATHEMATICAL FRAKTUR SMALL G
.. |Hfr| unicode:: U+0210C .. BLACK-LETTER CAPITAL H
.. |hfr| unicode:: U+1D525 .. MATHEMATICAL FRAKTUR SMALL H
.. |Ifr| unicode:: U+02111 .. BLACK-LETTER CAPITAL I
.. |ifr| unicode:: U+1D526 .. MATHEMATICAL FRAKTUR SMALL I
.. |Jfr| unicode:: U+1D50D .. MATHEMATICAL FRAKTUR CAPITAL J
.. |jfr| unicode:: U+1D527 .. MATHEMATICAL FRAKTUR SMALL J
.. |Kfr| unicode:: U+1D50E .. MATHEMATICAL FRAKTUR CAPITAL K
.. |kfr| unicode:: U+1D528 .. MATHEMATICAL FRAKTUR SMALL K
.. |Lfr| unicode:: U+1D50F .. MATHEMATICAL FRAKTUR CAPITAL L
.. |lfr| unicode:: U+1D529 .. MATHEMATICAL FRAKTUR SMALL L
.. |Mfr| unicode:: U+1D510 .. MATHEMATICAL FRAKTUR CAPITAL M
.. |mfr| unicode:: U+1D52A .. MATHEMATICAL FRAKTUR SMALL M
.. |Nfr| unicode:: U+1D511 .. MATHEMATICAL FRAKTUR CAPITAL N
.. |nfr| unicode:: U+1D52B .. MATHEMATICAL FRAKTUR SMALL N
.. |Ofr| unicode:: U+1D512 .. MATHEMATICAL FRAKTUR CAPITAL O
.. |ofr| unicode:: U+1D52C .. MATHEMATICAL FRAKTUR SMALL O
.. |Pfr| unicode:: U+1D513 .. MATHEMATICAL FRAKTUR CAPITAL P
.. |pfr| unicode:: U+1D52D .. MATHEMATICAL FRAKTUR SMALL P
.. |Qfr| unicode:: U+1D514 .. MATHEMATICAL FRAKTUR CAPITAL Q
.. |qfr| unicode:: U+1D52E .. MATHEMATICAL FRAKTUR SMALL Q
.. |Rfr| unicode:: U+0211C .. BLACK-LETTER CAPITAL R
.. |rfr| unicode:: U+1D52F .. MATHEMATICAL FRAKTUR SMALL R
.. |Sfr| unicode:: U+1D516 .. MATHEMATICAL FRAKTUR CAPITAL S
.. |sfr| unicode:: U+1D530 .. MATHEMATICAL FRAKTUR SMALL S
.. |Tfr| unicode:: U+1D517 .. MATHEMATICAL FRAKTUR CAPITAL T
.. |tfr| unicode:: U+1D531 .. MATHEMATICAL FRAKTUR SMALL T
.. |Ufr| unicode:: U+1D518 .. MATHEMATICAL FRAKTUR CAPITAL U
.. |ufr| unicode:: U+1D532 .. MATHEMATICAL FRAKTUR SMALL U
.. |Vfr| unicode:: U+1D519 .. MATHEMATICAL FRAKTUR CAPITAL V
.. |vfr| unicode:: U+1D533 .. MATHEMATICAL FRAKTUR SMALL V
.. |Wfr| unicode:: U+1D51A .. MATHEMATICAL FRAKTUR CAPITAL W
.. |wfr| unicode:: U+1D534 .. MATHEMATICAL FRAKTUR SMALL W
.. |Xfr| unicode:: U+1D51B .. MATHEMATICAL FRAKTUR CAPITAL X
.. |xfr| unicode:: U+1D535 .. MATHEMATICAL FRAKTUR SMALL X
.. |Yfr| unicode:: U+1D51C .. MATHEMATICAL FRAKTUR CAPITAL Y
.. |yfr| unicode:: U+1D536 .. MATHEMATICAL FRAKTUR SMALL Y
.. |Zfr| unicode:: U+02128 .. BLACK-LETTER CAPITAL Z
.. |zfr| unicode:: U+1D537 .. MATHEMATICAL FRAKTUR SMALL Z
