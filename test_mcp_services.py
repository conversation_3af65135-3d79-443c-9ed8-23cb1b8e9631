#!/usr/bin/env python3
"""
测试MCP服务
验证MiniMax MCP和AllVoiceLab MCP是否正常工作
"""

import asyncio
import os
import sys
import subprocess
import json
from pathlib import Path

# 使用MCP环境
MCP_PYTHON = r"c:\StudentsCMSSP\mcp_env\Scripts\python.exe"

async def test_minimax_mcp():
    """测试MiniMax MCP"""
    print("🔍 测试MiniMax MCP...")
    
    try:
        # 检查是否安装
        result = subprocess.run([
            MCP_PYTHON, "-c", "import minimax_mcp; print('✅ MiniMax MCP已安装')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout.strip())
            return True
        else:
            print(f"❌ MiniMax MCP导入失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ MiniMax MCP测试超时")
        return False
    except Exception as e:
        print(f"❌ MiniMax MCP测试失败: {e}")
        return False

async def test_allvoicelab_mcp():
    """测试AllVoiceLab MCP"""
    print("🔍 测试AllVoiceLab MCP...")
    
    try:
        # 检查是否安装
        result = subprocess.run([
            MCP_PYTHON, "-c", "import allvoicelab_mcp; print('✅ AllVoiceLab MCP已安装')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout.strip())
            return True
        else:
            print(f"❌ AllVoiceLab MCP导入失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ AllVoiceLab MCP测试超时")
        return False
    except Exception as e:
        print(f"❌ AllVoiceLab MCP测试失败: {e}")
        return False

async def test_uvx_availability():
    """测试uvx是否可用"""
    print("🔍 测试uvx可用性...")
    
    try:
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ uvx可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ uvx不可用，需要安装uv")
            print("安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh")
            return False
            
    except FileNotFoundError:
        print("❌ uvx未找到，需要安装uv")
        print("Windows安装: powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"")
        return False
    except Exception as e:
        print(f"❌ uvx测试失败: {e}")
        return False

async def test_mcp_server_startup():
    """测试MCP服务器启动"""
    print("🔍 测试MCP服务器启动...")
    
    # 测试MiniMax MCP服务器
    try:
        print("  测试MiniMax MCP服务器...")
        process = subprocess.Popen([
            "uvx", "minimax-mcp"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待2秒看是否能正常启动
        await asyncio.sleep(2)
        
        if process.poll() is None:
            print("  ✅ MiniMax MCP服务器启动成功")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ MiniMax MCP服务器启动失败: {stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ MiniMax MCP服务器测试失败: {e}")
        return False

def check_config_file():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    config_file = "claude_desktop_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查MCP服务器配置
        mcp_servers = config.get('mcpServers', {})
        
        if 'MiniMax' in mcp_servers:
            print("  ✅ MiniMax配置存在")
            minimax_config = mcp_servers['MiniMax']
            if minimax_config.get('env', {}).get('MINIMAX_API_KEY'):
                print("  ✅ MiniMax API密钥已配置")
            else:
                print("  ⚠️  MiniMax API密钥未配置")
        else:
            print("  ❌ MiniMax配置缺失")
        
        if 'AllVoiceLab' in mcp_servers:
            print("  ✅ AllVoiceLab配置存在")
        else:
            print("  ❌ AllVoiceLab配置缺失")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def check_output_directory():
    """检查输出目录"""
    print("🔍 检查输出目录...")
    
    output_dir = Path("output")
    
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
        print("✅ 输出目录已创建")
    else:
        print("✅ 输出目录存在")
    
    return True

async def main():
    """主测试函数"""
    print("=" * 60)
    print("MCP服务测试")
    print("=" * 60)
    
    # 测试项目
    tests = [
        ("配置文件检查", check_config_file),
        ("输出目录检查", check_output_directory),
        ("uvx可用性", test_uvx_availability),
        ("MiniMax MCP", test_minimax_mcp),
        ("AllVoiceLab MCP", test_allvoicelab_mcp),
        ("MCP服务器启动", test_mcp_server_startup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP环境准备就绪")
        print("\n下一步:")
        print("1. 在Claude Desktop中配置MCP服务器")
        print("2. 重启Claude Desktop")
        print("3. 测试MCP功能")
        return True
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        return False

if __name__ == "__main__":
    asyncio.run(main())
