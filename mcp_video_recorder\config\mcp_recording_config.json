{"mcp": {"server_command": ["python", "mcp_server.py"], "server_args": [], "timeout": 30}, "recording": {"fps": 30, "resolution": [1920, 1080], "audio_sample_rate": 44100, "audio_channels": 2, "video_codec": "mp4v", "audio_codec": "pcm"}, "tts": {"engine": "local", "rate": 150, "volume": 0.9, "voice_id": null, "language": "zh-CN"}, "browser": {"headless": false, "slow_mo": 1000, "viewport": {"width": 1920, "height": 1080}, "timeout": 30000}, "output": {"directory": "output/realtime/", "filename": "realtime_recording_{timestamp}.mp4", "temp_directory": "temp/", "keep_temp_files": false}, "sync": {"audio_video_sync": true, "operation_delay": 0.5, "scene_transition_delay": 1.0, "narration_operation_overlap": true}, "quality": {"video_bitrate": "5000k", "audio_bitrate": "128k", "compression_level": "medium"}, "logging": {"level": "INFO", "file": "logs/mcp_recording.log", "max_size": "10MB", "backup_count": 3}}