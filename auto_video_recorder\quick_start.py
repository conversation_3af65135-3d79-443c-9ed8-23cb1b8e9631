#!/usr/bin/env python3
"""
StudentsCMSSP 自动化视频录制器 - 快速启动脚本
简化版本，适合快速测试和演示
"""

import asyncio
import json
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 简化的配置
SIMPLE_CONFIG = {
    "script_file": "食谱管理模块视频录制脚本.md",
    "browser": {
        "headless": False,
        "slow_mo": 2000,  # 慢速演示
        "viewport": {"width": 1920, "height": 1080}
    },
    "tts": {
        "engine": "local",  # 使用本地TTS，无需API密钥
        "rate": 150,
        "volume": 0.9
    },
    "output": {
        "directory": "output/",
        "filename": "demo_video_{timestamp}.mp4"
    }
}

class SimpleVideoRecorder:
    """简化的视频录制器"""
    
    def __init__(self):
        self.config = SIMPLE_CONFIG
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('quick_recording.log')
            ]
        )
        self.logger = logging.getLogger('SimpleRecorder')
    
    async def record_demo(self):
        """录制演示视频"""
        self.logger.info("开始快速录制演示...")
        
        try:
            # 检查依赖
            await self._check_dependencies()
            
            # 简化的录制流程
            await self._simple_recording_flow()
            
            self.logger.info("演示录制完成!")
            
        except Exception as e:
            self.logger.error(f"录制失败: {e}")
            raise
    
    async def _check_dependencies(self):
        """检查基本依赖"""
        try:
            import playwright
            from playwright.async_api import async_playwright
            self.logger.info("✅ Playwright 可用")
        except ImportError:
            self.logger.error("❌ 请先安装 Playwright: pip install playwright")
            raise
        
        try:
            import pyttsx3
            self.logger.info("✅ 本地TTS 可用")
        except ImportError:
            self.logger.error("❌ 请先安装 pyttsx3: pip install pyttsx3")
            raise
    
    async def _simple_recording_flow(self):
        """简化的录制流程"""
        from playwright.async_api import async_playwright
        
        # 启动浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            headless=False,
            slow_mo=2000,
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            record_video_dir='output/'
        )
        
        page = await context.new_page()
        
        try:
            # 演示场景1: 访问首页
            self.logger.info("场景1: 访问系统首页")
            await page.goto('http://127.0.0.1:8080')
            await asyncio.sleep(3)
            
            # 演示场景2: 进入食谱管理
            self.logger.info("场景2: 进入食谱管理")
            try:
                await page.click('text="食谱管理"')
                await asyncio.sleep(2)
            except:
                self.logger.warning("未找到食谱管理菜单，跳过")
            
            # 演示场景3: 查看食谱列表
            self.logger.info("场景3: 查看食谱列表")
            await page.goto('http://127.0.0.1:8080/recipe/')
            await asyncio.sleep(3)
            
            # 演示场景4: 添加食谱
            self.logger.info("场景4: 演示添加食谱")
            try:
                await page.click('text="添加食谱"')
                await asyncio.sleep(2)
                
                # 填写表单
                await page.fill('input[name="name"]', '演示食谱')
                await asyncio.sleep(1)
                
                await page.fill('textarea[name="description"]', '这是一个演示食谱')
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.warning(f"添加食谱演示失败: {e}")
            
            # 等待录制完成
            await asyncio.sleep(3)
            
        finally:
            await context.close()
            await browser.close()
            await playwright.stop()
    
    def generate_simple_audio(self, text: str, output_file: str):
        """生成简单的音频解说"""
        try:
            import pyttsx3
            
            engine = pyttsx3.init()
            engine.setProperty('rate', self.config['tts']['rate'])
            engine.setProperty('volume', self.config['tts']['volume'])
            
            # 设置中文语音（如果可用）
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                    engine.setProperty('voice', voice.id)
                    break
            
            engine.save_to_file(text, output_file)
            engine.runAndWait()
            
            self.logger.info(f"音频生成完成: {output_file}")
            
        except Exception as e:
            self.logger.error(f"音频生成失败: {e}")

def check_system_ready():
    """检查系统是否准备就绪"""
    print("检查系统状态...")
    
    # 检查StudentsCMSSP是否运行
    import requests
    try:
        response = requests.get('http://127.0.0.1:8080', timeout=5)
        if response.status_code == 200:
            print("✅ StudentsCMSSP 系统运行正常")
            return True
        else:
            print(f"⚠️  系统响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ StudentsCMSSP 系统未运行")
        print("请先启动系统: python run.py")
        return False

def install_basic_deps():
    """安装基本依赖"""
    print("安装基本依赖...")

    import subprocess

    # 首先升级pip
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
                     check=True, capture_output=True)
        print("✅ pip 升级完成")
    except subprocess.CalledProcessError:
        print("⚠️  pip 升级失败，继续安装...")

    # 使用最小依赖文件
    try:
        if os.path.exists('requirements_minimal.txt'):
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements_minimal.txt'],
                         check=True)
            print("✅ 最小依赖安装完成")
        else:
            # 手动安装核心依赖
            deps = ['playwright>=1.40.0', 'pyttsx3>=2.90', 'requests>=2.31.0']
            for dep in deps:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep],
                             check=True, capture_output=True)
                print(f"✅ {dep} 安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

    # 安装Playwright浏览器
    try:
        subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'],
                     check=True, capture_output=True)
        print("✅ Playwright浏览器安装完成")
    except subprocess.CalledProcessError:
        print("❌ Playwright浏览器安装失败")
        print("请手动运行: python -m playwright install chromium")
        return False

    return True

async def main():
    """主函数"""
    print("=" * 60)
    print("StudentsCMSSP 自动化视频录制器 - 快速启动")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 检查系统状态
    if not check_system_ready():
        print("\n请先启动StudentsCMSSP系统，然后重新运行此脚本")
        return
    
    # 询问是否安装依赖
    install_deps = input("\n是否需要安装基本依赖? (y/n): ").lower().strip()
    if install_deps == 'y':
        if not install_basic_deps():
            print("依赖安装失败，请手动安装")
            return
    
    # 开始录制
    print("\n开始录制演示视频...")
    print("注意: 录制过程中请不要操作鼠标和键盘")
    
    input("按回车键开始录制...")
    
    try:
        recorder = SimpleVideoRecorder()
        await recorder.record_demo()
        
        print("\n" + "=" * 60)
        print("🎉 录制完成!")
        print("📁 视频文件保存在 output/ 目录中")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n用户中断录制")
    except Exception as e:
        print(f"\n❌ 录制失败: {e}")
        print("请检查错误日志: quick_recording.log")

if __name__ == "__main__":
    # Windows兼容性设置
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
