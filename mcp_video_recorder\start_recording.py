#!/usr/bin/env python3
"""
MCP实时录制启动脚本
简化的启动流程，支持快速开始录制
"""

import asyncio
import os
import sys
import json
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("检查依赖...")
    
    required_packages = [
        'mcp',
        'playwright', 
        'pyttsx3',
        'pyaudio',
        'cv2',
        'PIL',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_studentscms():
    """检查StudentsCMSSP系统"""
    print("\n检查StudentsCMSSP系统...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8080', timeout=5)
        if response.status_code == 200:
            print("✅ StudentsCMSSP系统运行正常")
            return True
        else:
            print(f"⚠️  系统响应异常: {response.status_code}")
            return False
    except Exception:
        print("❌ StudentsCMSSP系统未运行")
        print("请先启动系统: python run.py")
        return False

def create_directories():
    """创建必要目录"""
    print("\n创建目录...")
    
    directories = [
        "output/realtime",
        "temp",
        "logs",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")

def setup_config():
    """设置配置"""
    config_file = "config/mcp_recording_config.json"
    
    if not os.path.exists(config_file):
        print(f"\n创建配置文件: {config_file}")
        
        default_config = {
            "mcp": {
                "server_command": ["python", "mcp_server.py"],
                "server_args": []
            },
            "recording": {
                "fps": 30,
                "resolution": [1920, 1080]
            },
            "tts": {
                "engine": "local",
                "rate": 150,
                "volume": 0.9
            },
            "browser": {
                "headless": False,
                "slow_mo": 1000,
                "viewport": {"width": 1920, "height": 1080}
            },
            "output": {
                "directory": "output/realtime/",
                "filename": "realtime_recording_{timestamp}.mp4"
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置文件创建完成")

async def start_mcp_server():
    """启动MCP服务器"""
    print("\n启动MCP服务器...")
    
    try:
        # 启动MCP服务器进程
        process = await asyncio.create_subprocess_exec(
            sys.executable, "mcp_server.py",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ MCP服务器启动成功")
        return process
        
    except Exception as e:
        print(f"❌ MCP服务器启动失败: {e}")
        return None

async def run_recording():
    """运行录制"""
    print("\n" + "=" * 60)
    print("开始MCP实时录制")
    print("=" * 60)
    
    # 选择录制模式
    print("\n选择录制模式:")
    print("1. 供应商模块完整演示 (30分钟)")
    print("2. 供应商模块快速演示 (10分钟)")
    print("3. 自定义录制脚本")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        # 完整演示
        from supplier_recording_script import main as supplier_main
        await supplier_main()
        
    elif choice == "2":
        # 快速演示
        await run_quick_demo()
        
    elif choice == "3":
        # 自定义脚本
        script_file = input("请输入脚本文件路径: ").strip()
        if os.path.exists(script_file):
            await run_custom_script(script_file)
        else:
            print("❌ 脚本文件不存在")
    else:
        print("无效选择，使用快速演示模式")
        await run_quick_demo()

async def run_quick_demo():
    """快速演示"""
    from mcp_realtime_recorder import MCPRealtimeRecorder
    
    # 简化的演示脚本
    quick_script = {
        "title": "供应商模块快速演示",
        "scenes": [
            {
                "title": "系统概览",
                "url": "http://127.0.0.1:8080",
                "narration": "欢迎来到StudentsCMSSP供应商管理模块快速演示。",
                "actions": [
                    {"type": "wait", "duration": 2}
                ]
            },
            {
                "title": "供应商列表",
                "url": "http://127.0.0.1:8080/supplier/",
                "narration": "这里是供应商管理的主界面，显示了所有供应商信息。",
                "actions": [
                    {"type": "wait", "duration": 3},
                    {"type": "scroll"}
                ]
            },
            {
                "title": "产品管理",
                "url": "http://127.0.0.1:8080/supplier-product/",
                "narration": "供应商产品管理界面，支持产品的添加、编辑和审核。",
                "actions": [
                    {"type": "wait", "duration": 3},
                    {"type": "scroll"}
                ]
            }
        ]
    }
    
    recorder = MCPRealtimeRecorder()
    await recorder.initialize()
    await recorder.start_realtime_recording(quick_script)

async def run_custom_script(script_file):
    """运行自定义脚本"""
    try:
        with open(script_file, 'r', encoding='utf-8') as f:
            script_data = json.load(f)
        
        from mcp_realtime_recorder import MCPRealtimeRecorder
        recorder = MCPRealtimeRecorder()
        await recorder.initialize()
        await recorder.start_realtime_recording(script_data)
        
    except Exception as e:
        print(f"❌ 运行自定义脚本失败: {e}")

async def main():
    """主函数"""
    print("=" * 60)
    print("StudentsCMSSP MCP实时录制器")
    print("=" * 60)
    
    # 检查环境
    if not check_dependencies():
        return
    
    if not check_studentscms():
        return
    
    # 准备环境
    create_directories()
    setup_config()
    
    # 启动MCP服务器
    mcp_process = await start_mcp_server()
    
    try:
        # 等待服务器启动
        await asyncio.sleep(2)
        
        # 开始录制
        await run_recording()
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n❌ 错误: {e}")
    finally:
        # 清理
        if mcp_process:
            mcp_process.terminate()
            await mcp_process.wait()
        
        print("\n录制结束")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
