#!/usr/bin/env python3
"""
基于MP3音频的同步录制器
使用您已有的MP3解说文件进行同步录制
"""

import asyncio
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 音频播放
import pygame
from pydub import AudioSegment
from pydub.playback import play

# 视频录制
import cv2
import numpy as np
from PIL import ImageGrab

# 浏览器控制
from playwright.async_api import async_playwright, Page, Browser

class MP3SyncRecorder:
    """基于MP3的同步录制器"""
    
    def __init__(self, mp3_file: str, script_file: str):
        self.mp3_file = mp3_file
        self.script_file = script_file
        
        # 录制状态
        self.is_recording = False
        self.start_time = None
        
        # 组件
        self.browser = None
        self.page = None
        self.video_writer = None
        
        # 音频和脚本数据
        self.audio_segment = None
        self.script_data = None
        
        # 配置
        self.config = {
            "recording": {
                "fps": 30,
                "resolution": [1920, 1080],
                "output_dir": "output/mp3_sync/"
            },
            "browser": {
                "headless": False,
                "slow_mo": 1000,
                "viewport": {"width": 1920, "height": 1080}
            }
        }
    
    async def initialize(self):
        """初始化"""
        print("初始化MP3同步录制器...")
        
        # 加载音频文件
        self._load_audio()
        
        # 加载脚本文件
        self._load_script()
        
        # 初始化浏览器
        await self._init_browser()
        
        # 初始化视频录制
        self._init_video_recorder()
        
        # 初始化音频播放
        pygame.mixer.init()
        
        print("初始化完成")
    
    def _load_audio(self):
        """加载音频文件"""
        try:
            self.audio_segment = AudioSegment.from_mp3(self.mp3_file)
            print(f"✅ 音频文件加载成功: {self.mp3_file}")
            print(f"音频时长: {len(self.audio_segment) / 1000:.1f} 秒")
        except Exception as e:
            raise Exception(f"加载音频文件失败: {e}")
    
    def _load_script(self):
        """加载脚本文件"""
        try:
            with open(self.script_file, 'r', encoding='utf-8') as f:
                self.script_data = json.load(f)
            print(f"✅ 脚本文件加载成功: {self.script_file}")
        except Exception as e:
            raise Exception(f"加载脚本文件失败: {e}")
    
    async def _init_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=self.config["browser"]["headless"],
            slow_mo=self.config["browser"]["slow_mo"],
            args=['--start-maximized']
        )
        
        context = await self.browser.new_context(
            viewport=self.config["browser"]["viewport"]
        )
        
        self.page = await context.new_page()
    
    def _init_video_recorder(self):
        """初始化视频录制"""
        fps = self.config["recording"]["fps"]
        resolution = tuple(self.config["recording"]["resolution"])
        
        # 创建输出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(self.config["recording"]["output_dir"])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.output_file = output_dir / f"mp3_sync_recording_{timestamp}.mp4"
        
        # 创建视频编写器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.video_writer = cv2.VideoWriter(
            str(self.output_file),
            fourcc,
            fps,
            resolution
        )
    
    async def start_recording(self):
        """开始录制"""
        print("开始MP3同步录制...")
        
        self.is_recording = True
        self.start_time = time.time()
        
        # 启动视频录制线程
        video_thread = threading.Thread(target=self._video_recording_loop)
        video_thread.daemon = True
        video_thread.start()
        
        # 启动音频播放线程
        audio_thread = threading.Thread(target=self._play_audio)
        audio_thread.daemon = True
        audio_thread.start()
        
        try:
            # 执行同步操作
            await self._execute_sync_actions()
        finally:
            await self._stop_recording()
    
    def _video_recording_loop(self):
        """视频录制循环"""
        fps = self.config["recording"]["fps"]
        resolution = tuple(self.config["recording"]["resolution"])
        
        while self.is_recording:
            try:
                # 截取屏幕
                screenshot = ImageGrab.grab()
                frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                frame = cv2.resize(frame, resolution)
                
                # 写入视频帧
                self.video_writer.write(frame)
                
                # 控制帧率
                time.sleep(1.0 / fps)
                
            except Exception as e:
                print(f"视频录制错误: {e}")
                break
    
    def _play_audio(self):
        """播放音频"""
        try:
            # 将音频段转换为pygame可播放的格式
            audio_data = self.audio_segment.raw_data
            pygame.mixer.music.load(self.mp3_file)
            pygame.mixer.music.play()
            
            print("🎵 开始播放音频")
            
        except Exception as e:
            print(f"音频播放错误: {e}")
    
    async def _execute_sync_actions(self):
        """执行同步操作"""
        scenes = self.script_data.get('scenes', [])
        
        for i, scene in enumerate(scenes):
            print(f"执行场景 {i+1}/{len(scenes)}: {scene.get('title', 'Unknown')}")
            
            # 计算场景开始时间
            scene_start_time = scene.get('start_time', 0)  # 秒
            
            # 等待到场景开始时间
            current_time = time.time() - self.start_time
            if scene_start_time > current_time:
                await asyncio.sleep(scene_start_time - current_time)
            
            try:
                # 导航到页面
                if scene.get('url'):
                    await self.page.goto(scene['url'])
                    await asyncio.sleep(1)
                
                # 执行操作
                actions = scene.get('actions', [])
                for action in actions:
                    await self._execute_action(action)
                
            except Exception as e:
                print(f"执行场景失败: {e}")
                continue
    
    async def _execute_action(self, action: Dict[str, Any]):
        """执行操作"""
        action_type = action.get('type', 'unknown')
        
        try:
            if action_type == 'click':
                selector = action.get('selector')
                if selector:
                    await self.page.click(selector)
                    
            elif action_type == 'type':
                selector = action.get('selector')
                text = action.get('text', '')
                if selector:
                    await self.page.fill(selector, text)
                    
            elif action_type == 'wait':
                duration = action.get('duration', 1)
                await asyncio.sleep(duration)
                
            elif action_type == 'scroll':
                await self.page.keyboard.press('PageDown')
                
            elif action_type == 'navigate':
                url = action.get('url')
                if url:
                    await self.page.goto(url)
                    
        except Exception as e:
            print(f"执行操作失败: {action_type}, {e}")
    
    async def _stop_recording(self):
        """停止录制"""
        print("停止录制...")
        
        self.is_recording = False
        
        # 停止音频播放
        pygame.mixer.music.stop()
        
        # 释放资源
        if self.video_writer:
            self.video_writer.release()
        
        if self.browser:
            await self.browser.close()
        
        print(f"录制完成: {self.output_file}")

# 脚本示例格式
SCRIPT_EXAMPLE = {
    "scenes": [
        {
            "title": "系统介绍",
            "start_time": 0,  # 音频开始后0秒
            "duration": 30,   # 持续30秒
            "url": "http://127.0.0.1:8080",
            "actions": [
                {"type": "wait", "duration": 3},
                {"type": "scroll"}
            ]
        },
        {
            "title": "供应商管理",
            "start_time": 30,  # 音频开始后30秒
            "duration": 60,    # 持续60秒
            "url": "http://127.0.0.1:8080/supplier/",
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".btn-primary"},
                {"type": "wait", "duration": 3}
            ]
        }
    ]
}

async def main():
    """主函数"""
    # 使用示例
    mp3_file = "path/to/your/narration.mp3"  # 您的MP3文件路径
    script_file = "path/to/your/script.json"  # 脚本文件路径
    
    # 检查文件是否存在
    if not Path(mp3_file).exists():
        print(f"❌ MP3文件不存在: {mp3_file}")
        return
    
    if not Path(script_file).exists():
        print(f"❌ 脚本文件不存在: {script_file}")
        print("创建示例脚本文件...")
        with open(script_file, 'w', encoding='utf-8') as f:
            json.dump(SCRIPT_EXAMPLE, f, ensure_ascii=False, indent=2)
        print(f"✅ 示例脚本已创建: {script_file}")
        print("请根据您的MP3内容调整脚本时间点")
        return
    
    recorder = MP3SyncRecorder(mp3_file, script_file)
    
    try:
        await recorder.initialize()
        
        print("准备开始录制...")
        print("注意：录制过程中请勿操作鼠标和键盘")
        input("按回车键开始录制...")
        
        await recorder.start_recording()
        print("录制完成！")
        
    except Exception as e:
        print(f"录制失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
