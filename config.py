import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'

    # CSRF配置
    WTF_CSRF_ENABLED = False  # 禁用CSRF保护，使用系统自带令牌
    WTF_CSRF_TIME_LIMIT = 3600  # CSRF令牌有效期，单位秒
    WTF_CSRF_SSL_STRICT = False  # 禁用严格的SSL检查
    WTF_CSRF_METHODS = ['PUT', 'PATCH', 'DELETE']  # 需要CSRF保护的方法，移除POST
    WTF_CSRF_HEADERS = ['X-CSRFToken', 'X-CSRF-Token']  # CSRF令牌的请求头

    # 数据库配置
    # 使用本地SQL Server配置（提高速度）
    # 使用URL编码的连接字符串避免转义问题
    from urllib.parse import quote_plus
    # 添加DATETIME精度参数
    # 本地SQL Server配置
    conn_str = "DRIVER={SQL Server};SERVER=(local)\\SQLEXPRESS;DATABASE=StudentsCMSSP;Trusted_Connection=yes"
    quoted_conn_str = quote_plus(conn_str)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    # 注意：SQLite配置已移除，系统将只使用SQL Server数据库

    SQLALCHEMY_TRACK_MODIFICATIONS = 0

    # 数据库连接池配置 - 优化性能
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 3600,        # 连接回收时间增加到1小时
        'pool_timeout': 30,          # 连接超时时间增加
        'pool_size': 20,             # 连接池大小增加
        'max_overflow': 10,          # 最大溢出连接数增加
        'pool_pre_ping': True,       # 连接前ping，确保连接有效
        'echo': False,               # 关闭SQL日志输出
    }

    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(basedir, 'app/static/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or '<EMAIL>'

    # 分页配置
    ITEMS_PER_PAGE = 10

    # API配置
    API_KEY = os.environ.get('API_KEY') or 'system_fix_api_key_2025'

    # CSRF配置
    # 使用Flask-WTF的默认CSRF字段名
    # WTF_CSRF_FIELD_NAME = 'csrf_token'

    # Redis配置
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB = int(os.environ.get('REDIS_DB') or 0)
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD') or None

    # 日志配置 - 优化性能，减少日志输出
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'WARNING'  # 只记录警告和错误
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'app.log')

    # 缓存配置 - 使用简单内存缓存提高性能
    CACHE_TYPE = 'simple'  # 使用内存缓存，比Redis更快
    CACHE_DEFAULT_TIMEOUT = 600  # 10分钟缓存

    # Redis配置保留，如需要可切换
    # CACHE_TYPE = 'redis'
    # CACHE_REDIS_HOST = REDIS_HOST
    # CACHE_REDIS_PORT = REDIS_PORT
    # CACHE_REDIS_DB = REDIS_DB
    # CACHE_REDIS_PASSWORD = REDIS_PASSWORD

    # 性能优化配置
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=12)  # 静态文件缓存12小时
    TEMPLATES_AUTO_RELOAD = False  # 关闭模板自动重载
    JSON_SORT_KEYS = False  # 关闭JSON键排序
    JSONIFY_PRETTYPRINT_REGULAR = False  # 关闭JSON美化

    @staticmethod
    def init_app(app):
        # 确保上传目录存在
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        # 确保日志目录存在
        os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)

        # 配置日志级别，减少不必要的日志输出
        import logging
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
        logging.getLogger('werkzeug').setLevel(logging.WARNING)

class DevelopmentConfig(Config):
    DEBUG = False  # 关闭调试模式提高性能

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

class ProductionConfig(Config):
    # 生产环境配置
    DEBUG = False
    TESTING = False

    # 生产环境应该使用环境变量设置敏感信息
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
