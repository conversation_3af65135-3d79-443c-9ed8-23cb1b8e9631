#!/usr/bin/env python3
"""
供应商模块实时录制脚本
基于MCP服务的实时音视频同步录制
"""

import asyncio
import json
import sys
import os

# 添加MCP环境路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mcp_env', 'Lib', 'site-packages'))

try:
    from mcp_realtime_recorder import MCPRealtimeRecorder
except ImportError:
    print("❌ 无法导入MCPRealtimeRecorder，请先运行MCP环境设置")
    sys.exit(1)

# 供应商模块录制脚本数据
SUPPLIER_RECORDING_SCRIPT = {
    "title": "StudentsCMSSP 供应商管理模块演示",
    "description": "完整的供应商管理功能演示，包括分类管理、供应商信息管理、产品管理等",
    "total_duration": 1800,  # 30分钟
    "scenes": [
        {
            "id": 1,
            "title": "系统介绍与登录",
            "duration": 120,  # 2分钟
            "url": "http://127.0.0.1:8080",
            "narration": """
            欢迎观看StudentsCMSSP学校食堂管理系统的供应商管理模块演示。
            供应商管理是食堂运营的基础环节，直接关系到食材质量、成本控制和食品安全。
            在StudentsCMSSP系统中，供应商模块不仅仅是简单的供应商信息管理，
            而是一个完整的供应链管理体系。让我们首先登录系统。
            """,
            "actions": [
                {"type": "wait", "duration": 3},
                {"type": "mcp_action", "tool_name": "get_system_status", "arguments": {}},
                {"type": "click", "selector": "input[name='username']"},
                {"type": "type", "selector": "input[name='username']", "text": "admin"},
                {"type": "click", "selector": "input[name='password']"},
                {"type": "type", "selector": "input[name='password']", "text": "admin"},
                {"type": "click", "selector": "button[type='submit']"},
                {"type": "wait", "duration": 3}
            ]
        },
        {
            "id": 2,
            "title": "供应商分类管理",
            "duration": 210,  # 3.5分钟
            "url": "http://127.0.0.1:8080/supplier-category/",
            "narration": """
            首先我们来看供应商分类管理。合理的分类体系是供应商管理的基础，
            它帮助我们按业务类型组织供应商，便于筛选和查找，支持差异化管理策略。
            让我们进入供应商分类管理页面。这里显示了所有的供应商分类，
            每个分类都显示了关联的供应商数量。我们可以看到分类的编辑和删除功能，
            删除前会检查是否有关联的供应商，确保数据完整性。
            现在让我们添加一个新的分类。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "mcp_action", "tool_name": "navigate_to_page", "arguments": {"url": "http://127.0.0.1:8080/supplier-category/"}},
                {"type": "scroll"},
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".btn-primary"},
                {"type": "wait", "duration": 1},
                {"type": "type", "selector": "input[name='name']", "text": "蔬菜供应商"},
                {"type": "type", "selector": "textarea[name='description']", "text": "专门提供新鲜蔬菜的供应商"},
                {"type": "click", "selector": "button[type='submit']"},
                {"type": "wait", "duration": 2}
            ]
        },
        {
            "id": 3,
            "title": "供应商基本信息管理",
            "duration": 270,  # 4.5分钟
            "url": "http://127.0.0.1:8080/supplier/",
            "narration": """
            供应商基本信息管理是整个供应链的核心。StudentsCMSSP采用了学校级数据隔离设计，
            确保不同学校的数据安全隔离，同时支持集团化管理。
            这里我们可以看到供应商列表，显示了ID、名称、分类、联系人、电话、合作学校、评级、状态等信息。
            系统支持搜索和筛选功能，让我们演示添加新供应商的过程。
            注意这里的一步完成设计，添加供应商的同时自动建立学校合作关系。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "mcp_action", "tool_name": "get_suppliers", "arguments": {"limit": 5}},
                {"type": "scroll"},
                {"type": "click", "selector": ".btn-success"},
                {"type": "wait", "duration": 2},
                {"type": "type", "selector": "input[name='name']", "text": "绿源农业合作社"},
                {"type": "type", "selector": "input[name='contact_person']", "text": "张经理"},
                {"type": "type", "selector": "input[name='phone']", "text": "***********"},
                {"type": "type", "selector": "input[name='email']", "text": "<EMAIL>"},
                {"type": "type", "selector": "textarea[name='address']", "text": "XX市XX区绿源路123号"},
                {"type": "type", "selector": "input[name='business_license']", "text": "91110000123456789X"},
                {"type": "select", "selector": "select[name='category_id']", "value": "1"},
                {"type": "click", "selector": "button[type='submit']"},
                {"type": "wait", "duration": 3}
            ]
        },
        {
            "id": 4,
            "title": "供应商产品管理",
            "duration": 300,  # 5分钟
            "url": "http://127.0.0.1:8080/supplier-product/",
            "narration": """
            供应商产品管理是连接供应商和食材的桥梁。每个产品都包含详细的质量认证、
            价格信息和规格参数，确保采购的透明性和可追溯性。
            这里显示了产品管理界面，我们可以看到产品状态：待审核、已审核、已拒绝、已上架。
            系统支持多维筛选功能，可以按供应商、食材、状态等条件筛选。
            现在让我们添加一个新产品，演示完整的产品信息录入过程。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "click", "selector": ".btn-primary"},
                {"type": "wait", "duration": 2},
                {"type": "select", "selector": "select[name='supplier_id']", "value": "1"},
                {"type": "select", "selector": "select[name='ingredient_id']", "value": "1"},
                {"type": "type", "selector": "input[name='product_code']", "text": "BC001"},
                {"type": "type", "selector": "input[name='product_name']", "text": "有机白菜"},
                {"type": "type", "selector": "input[name='specification']", "text": "500g/包"},
                {"type": "type", "selector": "input[name='price']", "text": "3.50"},
                {"type": "type", "selector": "input[name='quality_cert']", "text": "有机产品认证"},
                {"type": "type", "selector": "input[name='quality_standard']", "text": "GB/T 19630"},
                {"type": "type", "selector": "textarea[name='description']", "text": "新鲜有机白菜，无农药残留"},
                {"type": "click", "selector": "button[type='submit']"},
                {"type": "wait", "duration": 3}
            ]
        },
        {
            "id": 5,
            "title": "产品批次管理",
            "duration": 330,  # 5.5分钟
            "url": "http://127.0.0.1:8080/product-batch/",
            "narration": """
            产品批次管理是StudentsCMSSP的创新功能，它允许供应商批量添加同类产品，
            大大提高了产品上架效率。这个功能特别适合大型供应商或者季节性产品的批量管理。
            我们可以看到批次管理主界面，显示了批次状态：待处理、已审核、已上架、已拒绝。
            现在让我们创建一个新批次，演示批量产品管理的强大功能。
            系统会自动生成批次号，我们可以选择食材分类和供应商。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".btn-success"},
                {"type": "wait", "duration": 2},
                {"type": "select", "selector": "select[name='category_id']", "value": "1"},
                {"type": "select", "selector": "select[name='supplier_id']", "value": "1"},
                {"type": "click", "selector": ".btn-next"},
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": "input[type='checkbox'][value='1']"},
                {"type": "click", "selector": "input[type='checkbox'][value='2']"},
                {"type": "click", "selector": "input[type='checkbox'][value='3']"},
                {"type": "click", "selector": ".btn-next"},
                {"type": "wait", "duration": 2},
                {"type": "select", "selector": "select[name='price_strategy']", "value": "individual"},
                {"type": "type", "selector": "input[name='quality_cert']", "text": "有机产品认证"},
                {"type": "type", "selector": "input[name='supply_cycle']", "text": "3"},
                {"type": "click", "selector": ".btn-next"},
                {"type": "wait", "duration": 3}
            ]
        },
        {
            "id": 6,
            "title": "产品上架审核",
            "duration": 240,  # 4分钟
            "url": "http://127.0.0.1:8080/supplier-product/",
            "narration": """
            产品上架审核是食品安全管理的重要环节。StudentsCMSSP建立了严格的三级审核机制：
            产品审核、批次审核、最终上架，确保每一个上架的产品都符合学校的质量标准。
            我们可以看到待审核产品列表，每个产品都有详细的信息展示。
            审核人员可以查看产品的供应商资质、产品规格、质量认证、价格信息等。
            系统支持单个产品审核和批量审核操作，大大提高了审核效率。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".filter-btn[data-status='0']"},
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".btn-info"},
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "click", "selector": ".btn-success"},
                {"type": "wait", "duration": 1},
                {"type": "type", "selector": "textarea[name='review_comment']", "text": "产品质量符合标准，审核通过"},
                {"type": "click", "selector": ".btn-confirm"},
                {"type": "wait", "duration": 2}
            ]
        },
        {
            "id": 7,
            "title": "系统集成与数据流转",
            "duration": 180,  # 3分钟
            "url": "http://127.0.0.1:8080/dashboard/",
            "narration": """
            供应商模块不是孤立的系统，它与StudentsCMSSP的其他模块深度集成，
            形成了完整的食堂管理生态系统。我们可以看到与采购模块的集成，
            从供应商产品库选择商品时价格会自动带入。与库存模块集成时，
            产品信息会自动关联，支持批次号追溯。与财务模块集成，
            实现应付账款管理和供应商对账功能。
            这种深度集成确保了数据的一致性和业务流程的完整性。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "mcp_action", "tool_name": "analyze_page_content", "arguments": {"extract_navigation": True}},
                {"type": "click", "selector": "a[href*='purchase']"},
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "click", "selector": "a[href*='inventory']"},
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "click", "selector": "a[href*='financial']"},
                {"type": "wait", "duration": 2}
            ]
        },
        {
            "id": 8,
            "title": "移动端适配展示",
            "duration": 120,  # 2分钟
            "url": "http://127.0.0.1:8080/supplier/",
            "narration": """
            StudentsCMSSP充分考虑了移动办公的需求，供应商模块在移动端有良好的适配和用户体验。
            我们可以看到响应式布局的效果，在移动端视图下，供应商信息以卡片式展示，
            触控操作得到了优化，界面简洁明了。移动端支持供应商信息查看、产品审核操作、
            快速搜索等功能，让管理人员可以随时随地进行供应商管理工作。
            """,
            "actions": [
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "wait", "duration": 2},
                {"type": "click", "selector": ".mobile-card"},
                {"type": "wait", "duration": 2},
                {"type": "scroll"},
                {"type": "wait", "duration": 2}
            ]
        },
        {
            "id": 9,
            "title": "总结与展望",
            "duration": 60,  # 1分钟
            "url": "http://127.0.0.1:8080/",
            "narration": """
            通过今天的演示，我们全面了解了StudentsCMSSP供应商管理模块的强大功能。
            它建立了标准化的供应商管理体系，严格的审核流程确保食品安全，
            批量操作和自动化流程大幅提升工作效率，学校级数据隔离保障信息安全，
            完整的追溯链从供应商到餐桌。StudentsCMSSP供应商管理模块为学校食堂
            提供了专业、可靠、易用的供应商管理解决方案，是现代化学校食堂管理的重要工具。
            感谢观看，如有疑问请联系技术支持团队。
            """,
            "actions": [
                {"type": "wait", "duration": 3},
                {"type": "mcp_action", "tool_name": "get_system_status", "arguments": {}},
                {"type": "wait", "duration": 2}
            ]
        }
    ]
}

async def main():
    """主录制函数"""
    print("=" * 60)
    print("StudentsCMSSP 供应商模块 - MCP实时录制")
    print("=" * 60)
    
    # 创建录制器
    recorder = MCPRealtimeRecorder("config/mcp_recording_config.json")
    
    try:
        # 初始化
        await recorder.initialize()
        print("✅ 录制器初始化完成")
        
        # 开始录制
        print("🎬 开始实时录制...")
        print("注意：录制过程中请勿操作鼠标和键盘")
        
        input("按回车键开始录制...")
        
        await recorder.start_realtime_recording(SUPPLIER_RECORDING_SCRIPT)
        
        print("🎉 录制完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断录制")
    except Exception as e:
        print(f"❌ 录制失败: {e}")
    finally:
        # 显示录制状态
        status = recorder.get_recording_status()
        if status["output_file"]:
            print(f"📁 输出文件: {status['output_file']}")

if __name__ == "__main__":
    asyncio.run(main())
