#!/usr/bin/env python3
"""
StudentsCMSSP 自动化视频录制器 - 修复版安装脚本
解决 winsound 依赖问题和其他常见安装问题
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("StudentsCMSSP 自动化视频录制器 - 修复版安装")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    if sys.version_info < (3, 8):
        print(f"❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    else:
        print(f"✅ Python版本检查通过: {platform.python_version()}")
        return True

def install_minimal_dependencies():
    """安装最小依赖包"""
    print("\n安装最小依赖包...")
    
    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, capture_output=True)
        
        # 安装最小依赖
        print("安装核心依赖...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements_minimal.txt"
        ], check=True)
        
        print("✅ 最小依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到 requirements_minimal.txt 文件")
        return False

def install_playwright_browser():
    """安装Playwright浏览器"""
    print("\n安装Playwright浏览器...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], check=True)
        
        print("✅ Playwright浏览器安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Playwright浏览器失败: {e}")
        return False

def create_directories():
    """创建必要目录"""
    print("\n创建目录结构...")
    
    directories = [
        "recordings/raw_videos",
        "recordings/audio", 
        "recordings/screenshots",
        "output/temp",
        "output/final",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 测试Playwright
        print("测试Playwright...")
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print("✅ Playwright测试通过")
        
        # 测试本地TTS
        print("测试本地TTS...")
        import pyttsx3
        engine = pyttsx3.init()
        engine.stop()
        print("✅ 本地TTS测试通过")
        
        # 测试winsound（仅Windows）
        if platform.system().lower() == "windows":
            print("测试winsound...")
            import winsound  # 这是标准库，无需安装
            print("✅ winsound可用（Python标准库）")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_studentscms_status():
    """检查StudentsCMSSP系统状态"""
    print("\n检查StudentsCMSSP系统状态...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8080', timeout=5)
        if response.status_code == 200:
            print("✅ StudentsCMSSP系统运行正常")
            return True
        else:
            print(f"⚠️  系统响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ StudentsCMSSP系统未运行")
        print("请先启动系统: python run.py")
        return False

def create_simple_config():
    """创建简化配置文件"""
    print("\n创建简化配置文件...")
    
    simple_config = {
        "script_file": "食谱管理模块视频录制脚本.md",
        "browser": {
            "headless": False,
            "slow_mo": 2000,
            "viewport": {"width": 1920, "height": 1080}
        },
        "tts": {
            "engine": "local",
            "rate": 150,
            "volume": 0.9
        },
        "recording": {
            "fps": 30,
            "format": "mp4"
        },
        "output": {
            "directory": "output/",
            "filename": "demo_video_{timestamp}.mp4"
        }
    }
    
    import json
    with open('config/simple_config.json', 'w', encoding='utf-8') as f:
        json.dump(simple_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 简化配置文件创建完成: config/simple_config.json")

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("🎉 安装完成!")
    print("=" * 60)
    print("使用方法:")
    print("1. 快速启动（推荐）:")
    print("   python quick_start.py")
    print()
    print("2. 完整功能（需要更多依赖）:")
    print("   pip install -r requirements.txt")
    print("   python main.py")
    print()
    print("3. 配置TTS（可选）:")
    print("   - 本地TTS: 已可用")
    print("   - Azure TTS: 需要API密钥")
    print("   - Google TTS: pip install gtts")
    print()
    print("注意事项:")
    print("- winsound是Python标准库，无需安装")
    print("- 确保StudentsCMSSP系统正在运行")
    print("- 录制时请勿操作鼠标键盘")
    print("=" * 60)

def main():
    """主安装流程"""
    print_header()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装最小依赖
    if not install_minimal_dependencies():
        print("\n❌ 依赖安装失败，请检查网络连接或手动安装")
        sys.exit(1)
    
    # 安装Playwright浏览器
    if not install_playwright_browser():
        print("\n❌ 浏览器安装失败，请手动运行: playwright install chromium")
        sys.exit(1)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n❌ 功能测试失败，请检查安装")
        sys.exit(1)
    
    # 创建简化配置
    create_simple_config()
    
    # 检查系统状态
    check_studentscms_status()
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
