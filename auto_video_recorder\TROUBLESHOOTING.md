# StudentsCMSSP 自动化视频录制器 - 故障排除指南

## 常见问题解决方案

### 1. winsound 安装错误

#### 问题描述
```
ERROR: Could not find a version that satisfies the requirement winsound==1.0
```

#### 解决方案
**重要**: `winsound` 是 Python 标准库的一部分，**无需通过 pip 安装**！

```python
# 正确用法 - 直接导入使用
import winsound

# 播放系统提示音
winsound.PlaySound("SystemExclamation", winsound.SND_ALIAS)

# 播放蜂鸣声
winsound.Beep(440, 500)  # 440Hz，持续500毫秒
```

#### 修复步骤
1. 从 `requirements.txt` 中删除 `winsound==1.0` 行
2. 使用修复版安装脚本：`python install_fix.py`
3. 或使用最小依赖：`pip install -r requirements_minimal.txt`

### 2. Python 版本兼容性问题

#### 问题描述
某些包要求 Python 3.10+，但当前环境版本较低。

#### 解决方案
1. **检查Python版本**
```bash
python --version
```

2. **升级Python**（推荐3.10+）
- 从 [Python官网](https://www.python.org/downloads/) 下载最新版本
- 安装时勾选 "Add to PATH"

3. **使用虚拟环境**
```bash
python -m venv video_recorder_env
video_recorder_env\Scripts\activate  # Windows
source video_recorder_env/bin/activate  # Linux/macOS
```

### 3. Playwright 浏览器安装失败

#### 问题描述
```
playwright install chromium 失败
```

#### 解决方案
1. **手动安装**
```bash
python -m playwright install chromium
```

2. **网络问题**
```bash
# 使用国内镜像
pip install playwright -i https://pypi.tuna.tsinghua.edu.cn/simple/
python -m playwright install chromium
```

3. **权限问题**（Linux/macOS）
```bash
sudo python -m playwright install-deps
python -m playwright install chromium
```

### 4. StudentsCMSSP 系统连接失败

#### 问题描述
```
❌ StudentsCMSSP 系统未运行
```

#### 解决方案
1. **启动系统**
```bash
cd c:\StudentsCMSSP
python run.py
```

2. **检查端口**
```bash
netstat -an | findstr :8080
```

3. **防火墙设置**
- 确保8080端口未被防火墙阻止
- 临时关闭防火墙测试

### 5. 音频生成失败

#### 问题描述
TTS音频生成失败或无声音。

#### 解决方案

**本地TTS（推荐用于测试）**
```python
import pyttsx3

engine = pyttsx3.init()
# 设置中文语音
voices = engine.getProperty('voices')
for voice in voices:
    if 'chinese' in voice.name.lower():
        engine.setProperty('voice', voice.id)
        break

engine.say("测试语音")
engine.runAndWait()
```

**Azure TTS（生产环境推荐）**
1. 获取Azure语音服务密钥
2. 配置 `config/recording_config.json`：
```json
{
  "tts": {
    "engine": "azure",
    "azure_key": "your_actual_key",
    "azure_region": "eastasia"
  }
}
```

### 6. 视频录制质量问题

#### 问题描述
录制的视频模糊或卡顿。

#### 解决方案
1. **调整录制参数**
```json
{
  "recording": {
    "fps": 30,
    "quality": "high",
    "resolution": "1920x1080"
  }
}
```

2. **系统性能优化**
- 关闭不必要的程序
- 确保足够的磁盘空间（至少5GB）
- 使用SSD存储录制文件

### 7. 内存不足问题

#### 问题描述
录制过程中内存占用过高。

#### 解决方案
1. **调整性能设置**
```json
{
  "performance": {
    "max_workers": 2,
    "memory_limit": "2GB",
    "temp_cleanup": true
  }
}
```

2. **分段录制**
- 将长视频分成多个短片段
- 逐段录制后合并

## 快速修复命令

### 完全重新安装
```bash
# 1. 清理环境
pip uninstall -y playwright moviepy opencv-python
rm -rf recordings/ output/ logs/

# 2. 使用修复版安装
python install_fix.py

# 3. 测试基本功能
python quick_start.py
```

### 最小化安装（仅核心功能）
```bash
pip install -r requirements_minimal.txt
python -m playwright install chromium
python quick_start.py
```

## 系统要求检查清单

- [ ] Python 3.8+ 已安装
- [ ] pip 已升级到最新版本
- [ ] StudentsCMSSP 系统正在运行 (http://127.0.0.1:8080)
- [ ] 至少 4GB 可用内存
- [ ] 至少 5GB 可用磁盘空间
- [ ] 稳定的网络连接
- [ ] 管理员权限（Windows）或sudo权限（Linux）

## 联系支持

如果以上方案都无法解决问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **电话**: 18373062333
- **QQ群**: [待补充]

提供以下信息有助于快速解决问题：
1. 操作系统版本
2. Python版本
3. 完整的错误信息
4. 安装日志文件

## 更新日志

### v1.0.1 (2025-06-23)
- 修复 winsound 依赖问题
- 添加最小依赖安装选项
- 优化错误处理和重试机制
- 改进安装脚本和文档
