# MCP实时录制器依赖包

# MCP核心
mcp>=1.0.0

# 浏览器自动化
playwright>=1.40.0

# 音频处理
pyttsx3>=2.90
pyaudio>=0.2.11

# 视频处理
opencv-python>=4.8.1.78
pillow>=10.1.0
numpy>=1.24.3

# 系统工具
psutil>=5.9.6
requests>=2.31.0

# 异步支持
asyncio-throttle>=1.0.2

# 日志和配置
colorlog>=6.8.0
pyyaml>=6.0.1

# 进度条
tqdm>=4.66.1

# Windows特定（音频支持）
pywin32>=306; sys_platform == "win32"

# macOS特定（音频支持）
pyobjc-framework-AVFoundation>=10.0; sys_platform == "darwin"

# Linux特定（音频支持）
python-xlib>=0.33; sys_platform == "linux"
