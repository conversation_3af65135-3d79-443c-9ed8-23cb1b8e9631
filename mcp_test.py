#!/usr/bin/env python3
"""
MCP集成测试脚本
验证MCP客户端和Playwright的基本功能
"""

import asyncio
import sys
import os

# 使用MCP环境的Python解释器
MCP_PYTHON = r"c:\StudentsCMSSP\mcp_env\Scripts\python.exe"

async def test_mcp_basic():
    """测试MCP基本功能"""
    print("🔍 测试MCP基本功能...")
    
    try:
        import mcp
        print("✅ MCP导入成功")
        
        # 测试MCP客户端创建
        from mcp import ClientSession
        print("✅ MCP ClientSession可用")
        
        return True
    except ImportError as e:
        print(f"❌ MCP导入失败: {e}")
        return False

async def test_playwright_basic():
    """测试Playwright基本功能"""
    print("🔍 测试Playwright基本功能...")
    
    try:
        from playwright.async_api import async_playwright
        print("✅ Playwright导入成功")
        
        # 测试浏览器启动
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 测试导航
        await page.goto("https://www.example.com")
        title = await page.title()
        print(f"✅ 浏览器测试成功，页面标题: {title}")
        
        await browser.close()
        await playwright.stop()
        
        return True
    except Exception as e:
        print(f"❌ Playwright测试失败: {e}")
        return False

async def test_studentscms_connection():
    """测试StudentsCMSSP连接"""
    print("🔍 测试StudentsCMSSP连接...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8080", timeout=5)
            if response.status_code == 200:
                print("✅ StudentsCMSSP系统连接成功")
                return True
            else:
                print(f"⚠️  StudentsCMSSP响应异常: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ StudentsCMSSP连接失败: {e}")
        print("请确保StudentsCMSSP系统正在运行: python run.py")
        return False

async def test_mcp_playwright_integration():
    """测试MCP和Playwright集成"""
    print("🔍 测试MCP和Playwright集成...")
    
    try:
        from playwright.async_api import async_playwright
        from mcp import ClientSession
        
        # 创建简单的MCP客户端会话
        # 注意：这里我们只是测试导入和基本创建，不连接实际的MCP服务器
        print("✅ MCP和Playwright可以同时导入")
        
        # 测试在Playwright中使用MCP相关功能
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 模拟MCP控制的浏览器操作
        await page.goto("http://127.0.0.1:8080")
        
        # 获取页面信息（模拟MCP工具调用）
        title = await page.title()
        url = page.url
        
        print(f"✅ MCP-Playwright集成测试成功")
        print(f"   页面标题: {title}")
        print(f"   页面URL: {url}")
        
        await browser.close()
        await playwright.stop()
        
        return True
        
    except Exception as e:
        print(f"❌ MCP-Playwright集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("MCP集成测试")
    print("=" * 60)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 运行测试
    tests = [
        ("MCP基本功能", test_mcp_basic),
        ("Playwright基本功能", test_playwright_basic),
        ("StudentsCMSSP连接", test_studentscms_connection),
        ("MCP-Playwright集成", test_mcp_playwright_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP环境准备就绪")
        return True
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        return False

if __name__ == "__main__":
    # 检查是否在MCP环境中运行
    if sys.executable != MCP_PYTHON:
        print(f"⚠️  当前不在MCP环境中")
        print(f"当前Python: {sys.executable}")
        print(f"MCP Python: {MCP_PYTHON}")
        print("\n请使用MCP环境运行此脚本:")
        print(f"{MCP_PYTHON} mcp_test.py")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
