#!/usr/bin/env python3
"""
StudentsCMSSP MCP服务端
为视频录制提供系统交互能力
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# StudentsCMSSP相关导入
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from app import create_app, db
    from app.models import Supplier, Recipe, WeeklyMenu
    from flask import current_app
except ImportError:
    print("警告: 无法导入StudentsCMSSP模块，某些功能可能不可用")

class StudentsCMSMCPServer:
    """StudentsCMSSP MCP服务端"""
    
    def __init__(self):
        self.server = Server("studentscms-mcp")
        self.app = None
        self.setup_tools()
        
    def setup_tools(self):
        """设置MCP工具"""
        
        # 供应商管理工具
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            return [
                Tool(
                    name="get_suppliers",
                    description="获取供应商列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {"type": "integer", "default": 10},
                            "category": {"type": "string", "description": "供应商分类"}
                        }
                    }
                ),
                Tool(
                    name="create_supplier",
                    description="创建新供应商",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "供应商名称"},
                            "contact_person": {"type": "string", "description": "联系人"},
                            "phone": {"type": "string", "description": "联系电话"},
                            "category_id": {"type": "integer", "description": "分类ID"}
                        },
                        "required": ["name", "contact_person", "phone"]
                    }
                ),
                Tool(
                    name="get_recipes",
                    description="获取食谱列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {"type": "integer", "default": 10},
                            "category": {"type": "string", "description": "食谱分类"}
                        }
                    }
                ),
                Tool(
                    name="create_recipe",
                    description="创建新食谱",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "食谱名称"},
                            "description": {"type": "string", "description": "食谱描述"},
                            "category": {"type": "string", "description": "食谱分类"}
                        },
                        "required": ["name", "description"]
                    }
                ),
                Tool(
                    name="get_system_status",
                    description="获取系统状态信息",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="navigate_to_page",
                    description="导航到指定页面并获取页面信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "目标URL"},
                            "wait_for": {"type": "string", "description": "等待的元素选择器"}
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="analyze_page_content",
                    description="分析当前页面内容",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "extract_forms": {"type": "boolean", "default": False},
                            "extract_tables": {"type": "boolean", "default": False},
                            "extract_navigation": {"type": "boolean", "default": True}
                        }
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            try:
                if name == "get_suppliers":
                    return await self._get_suppliers(arguments)
                elif name == "create_supplier":
                    return await self._create_supplier(arguments)
                elif name == "get_recipes":
                    return await self._get_recipes(arguments)
                elif name == "create_recipe":
                    return await self._create_recipe(arguments)
                elif name == "get_system_status":
                    return await self._get_system_status(arguments)
                elif name == "navigate_to_page":
                    return await self._navigate_to_page(arguments)
                elif name == "analyze_page_content":
                    return await self._analyze_page_content(arguments)
                else:
                    return [TextContent(type="text", text=f"未知工具: {name}")]
                    
            except Exception as e:
                return [TextContent(type="text", text=f"工具执行错误: {str(e)}")]
    
    async def _get_suppliers(self, args: Dict[str, Any]) -> List[TextContent]:
        """获取供应商列表"""
        try:
            if not self.app:
                await self._init_app()
            
            with self.app.app_context():
                limit = args.get('limit', 10)
                category = args.get('category')
                
                query = Supplier.query
                if category:
                    query = query.filter(Supplier.category.like(f'%{category}%'))
                
                suppliers = query.limit(limit).all()
                
                result = {
                    "count": len(suppliers),
                    "suppliers": [
                        {
                            "id": s.id,
                            "name": s.name,
                            "contact_person": s.contact_person,
                            "phone": s.phone,
                            "category": getattr(s, 'category', 'Unknown')
                        }
                        for s in suppliers
                    ]
                }
                
                return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
                
        except Exception as e:
            return [TextContent(type="text", text=f"获取供应商列表失败: {str(e)}")]
    
    async def _create_supplier(self, args: Dict[str, Any]) -> List[TextContent]:
        """创建新供应商"""
        try:
            if not self.app:
                await self._init_app()
            
            with self.app.app_context():
                supplier = Supplier(
                    name=args['name'],
                    contact_person=args['contact_person'],
                    phone=args['phone'],
                    category_id=args.get('category_id')
                )
                
                db.session.add(supplier)
                db.session.commit()
                
                result = {
                    "success": True,
                    "supplier_id": supplier.id,
                    "message": f"供应商 '{supplier.name}' 创建成功"
                }
                
                return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
                
        except Exception as e:
            return [TextContent(type="text", text=f"创建供应商失败: {str(e)}")]
    
    async def _get_recipes(self, args: Dict[str, Any]) -> List[TextContent]:
        """获取食谱列表"""
        try:
            if not self.app:
                await self._init_app()
            
            with self.app.app_context():
                limit = args.get('limit', 10)
                category = args.get('category')
                
                query = Recipe.query
                if category:
                    query = query.filter(Recipe.category.like(f'%{category}%'))
                
                recipes = query.limit(limit).all()
                
                result = {
                    "count": len(recipes),
                    "recipes": [
                        {
                            "id": r.id,
                            "name": r.name,
                            "description": r.description,
                            "category": getattr(r, 'category', 'Unknown')
                        }
                        for r in recipes
                    ]
                }
                
                return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
                
        except Exception as e:
            return [TextContent(type="text", text=f"获取食谱列表失败: {str(e)}")]
    
    async def _create_recipe(self, args: Dict[str, Any]) -> List[TextContent]:
        """创建新食谱"""
        try:
            if not self.app:
                await self._init_app()
            
            with self.app.app_context():
                recipe = Recipe(
                    name=args['name'],
                    description=args['description'],
                    category=args.get('category', '其他')
                )
                
                db.session.add(recipe)
                db.session.commit()
                
                result = {
                    "success": True,
                    "recipe_id": recipe.id,
                    "message": f"食谱 '{recipe.name}' 创建成功"
                }
                
                return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
                
        except Exception as e:
            return [TextContent(type="text", text=f"创建食谱失败: {str(e)}")]
    
    async def _get_system_status(self, args: Dict[str, Any]) -> List[TextContent]:
        """获取系统状态"""
        try:
            import requests
            import psutil
            
            # 检查系统运行状态
            try:
                response = requests.get('http://127.0.0.1:8080', timeout=5)
                system_running = response.status_code == 200
            except:
                system_running = False
            
            # 获取系统资源信息
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            result = {
                "system_running": system_running,
                "system_url": "http://127.0.0.1:8080",
                "resources": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent
                },
                "timestamp": asyncio.get_event_loop().time()
            }
            
            return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
            
        except Exception as e:
            return [TextContent(type="text", text=f"获取系统状态失败: {str(e)}")]
    
    async def _navigate_to_page(self, args: Dict[str, Any]) -> List[TextContent]:
        """导航到指定页面"""
        url = args['url']
        wait_for = args.get('wait_for')
        
        result = {
            "action": "navigate",
            "url": url,
            "wait_for": wait_for,
            "timestamp": asyncio.get_event_loop().time(),
            "message": f"导航到页面: {url}"
        }
        
        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    async def _analyze_page_content(self, args: Dict[str, Any]) -> List[TextContent]:
        """分析页面内容"""
        extract_forms = args.get('extract_forms', False)
        extract_tables = args.get('extract_tables', False)
        extract_navigation = args.get('extract_navigation', True)
        
        result = {
            "action": "analyze_page",
            "extract_forms": extract_forms,
            "extract_tables": extract_tables,
            "extract_navigation": extract_navigation,
            "timestamp": asyncio.get_event_loop().time(),
            "message": "页面内容分析完成"
        }
        
        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]
    
    async def _init_app(self):
        """初始化Flask应用"""
        try:
            self.app = create_app()
            print("Flask应用初始化成功")
        except Exception as e:
            print(f"Flask应用初始化失败: {e}")
            # 创建一个模拟应用用于测试
            from flask import Flask
            self.app = Flask(__name__)
    
    async def run(self):
        """运行MCP服务器"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                self.server.create_initialization_options()
            )

async def main():
    """主函数"""
    server = StudentsCMSMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
