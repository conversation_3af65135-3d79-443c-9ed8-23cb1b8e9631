#!/usr/bin/env python3
"""
StudentsCMSSP 自动化视频录制主程序
支持全自动的视频录制、音频合成和后期制作
"""

import asyncio
import json
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from scripts.script_parser import ScriptParser
from recorder.browser_controller import BrowserController
from recorder.audio_generator import AudioGenerator
from recorder.screen_recorder import ScreenRecorder
from editor.video_editor import VideoEditor
from utils.logger import setup_logger
from utils.file_manager import FileManager

class AutoVideoRecorder:
    """自动化视频录制器主类"""
    
    def __init__(self, config_file: str = 'config/recording_config.json'):
        """初始化录制器"""
        self.config_file = config_file
        self.config = self._load_config()
        self.logger = setup_logger('AutoVideoRecorder')
        
        # 初始化各个组件
        self.script_parser = ScriptParser(self.config['script_file'])
        self.browser_controller = BrowserController(self.config['browser'])
        self.audio_generator = AudioGenerator(self.config['tts'])
        self.screen_recorder = ScreenRecorder(self.config['recording'])
        self.video_editor = VideoEditor(self.config['video'])
        self.file_manager = FileManager(self.config['output'])
        
        # 创建必要的目录
        self._create_directories()
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"配置文件不存在: {self.config_file}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            sys.exit(1)
    
    def _create_directories(self):
        """创建必要的目录结构"""
        directories = [
            'recordings/raw_videos',
            'recordings/audio',
            'recordings/screenshots',
            'output/temp',
            'output/final',
            'logs'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    async def record_video(self) -> str:
        """录制完整视频的主流程"""
        self.logger.info("开始自动化视频录制流程...")
        
        try:
            # 1. 解析录制脚本
            scenes = await self._parse_script()
            
            # 2. 生成音频文件
            await self._generate_audio_files(scenes)
            
            # 3. 初始化浏览器和录制环境
            await self._initialize_recording_environment()
            
            # 4. 录制视频片段
            video_files = await self._record_video_scenes(scenes)
            
            # 5. 后期制作和合成
            final_video = await self._create_final_video(scenes, video_files)
            
            # 6. 清理临时文件
            await self._cleanup()
            
            self.logger.info(f"视频录制完成: {final_video}")
            return final_video
            
        except Exception as e:
            self.logger.error(f"录制过程中发生错误: {e}")
            await self._cleanup()
            raise
    
    async def _parse_script(self) -> list:
        """解析录制脚本"""
        self.logger.info("解析录制脚本...")
        
        scenes = self.script_parser.parse_markdown_script()
        self.logger.info(f"解析到 {len(scenes)} 个录制场景")
        
        # 验证脚本完整性
        for i, scene in enumerate(scenes):
            if not scene.get('narration'):
                self.logger.warning(f"场景 {i+1} 缺少解说词")
            if not scene.get('actions'):
                self.logger.warning(f"场景 {i+1} 缺少操作步骤")
        
        return scenes
    
    async def _generate_audio_files(self, scenes: list):
        """生成所有场景的音频文件"""
        self.logger.info("生成音频文件...")
        
        for i, scene in enumerate(scenes):
            if scene.get('narration'):
                audio_file = f"recordings/audio/scene_{i:02d}.wav"
                self.logger.info(f"生成场景 {i+1} 音频: {scene['title']}")
                
                try:
                    await asyncio.to_thread(
                        self.audio_generator.generate_audio,
                        scene['narration'],
                        audio_file
                    )
                    scene['audio_file'] = audio_file
                except Exception as e:
                    self.logger.error(f"生成音频失败 (场景 {i+1}): {e}")
                    scene['audio_file'] = None
    
    async def _initialize_recording_environment(self):
        """初始化录制环境"""
        self.logger.info("初始化录制环境...")
        
        # 初始化浏览器
        await self.browser_controller.initialize()
        
        # 初始化屏幕录制器
        await self.screen_recorder.initialize()
        
        # 等待环境稳定
        await asyncio.sleep(2)
    
    async def _record_video_scenes(self, scenes: list) -> list:
        """录制所有视频场景"""
        self.logger.info("开始录制视频场景...")
        
        video_files = []
        
        for i, scene in enumerate(scenes):
            self.logger.info(f"录制场景 {i+1}/{len(scenes)}: {scene['title']}")
            
            try:
                # 开始录制
                recording_file = f"recordings/raw_videos/scene_{i:02d}.mp4"
                await self.screen_recorder.start_recording(recording_file)
                
                # 执行场景操作
                await self.browser_controller.execute_scene(scene)
                
                # 停止录制
                await self.screen_recorder.stop_recording()
                
                video_files.append(recording_file)
                scene['video_file'] = recording_file
                
                # 场景间隔
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"录制场景 {i+1} 失败: {e}")
                video_files.append(None)
                scene['video_file'] = None
        
        return video_files
    
    async def _create_final_video(self, scenes: list, video_files: list) -> str:
        """创建最终视频"""
        self.logger.info("开始后期制作和视频合成...")
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = self.config['output']['filename'].format(timestamp=timestamp)
        output_path = os.path.join(self.config['output']['directory'], output_filename)
        
        # 执行视频编辑
        final_video = await asyncio.to_thread(
            self.video_editor.create_final_video,
            scenes,
            output_path
        )
        
        return final_video
    
    async def _cleanup(self):
        """清理资源和临时文件"""
        self.logger.info("清理资源...")
        
        try:
            # 关闭浏览器
            if hasattr(self.browser_controller, 'browser') and self.browser_controller.browser:
                await self.browser_controller.browser.close()
            
            # 停止屏幕录制
            await self.screen_recorder.cleanup()
            
            # 清理临时文件（可选）
            if self.config.get('cleanup_temp_files', False):
                self.file_manager.cleanup_temp_files()
                
        except Exception as e:
            self.logger.error(f"清理过程中发生错误: {e}")

async def main():
    """主函数"""
    print("StudentsCMSSP 自动化视频录制器")
    print("=" * 50)
    
    # 检查配置文件
    config_file = 'config/recording_config.json'
    if not os.path.exists(config_file):
        print(f"错误: 配置文件不存在 - {config_file}")
        print("请先创建配置文件，参考 config/recording_config.example.json")
        sys.exit(1)
    
    # 创建录制器实例
    recorder = AutoVideoRecorder(config_file)
    
    try:
        # 开始录制
        final_video = await recorder.record_video()
        
        print("\n" + "=" * 50)
        print("🎉 视频录制完成!")
        print(f"📁 输出文件: {final_video}")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n用户中断录制")
        await recorder._cleanup()
    except Exception as e:
        print(f"\n❌ 录制失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())
