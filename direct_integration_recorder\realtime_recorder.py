#!/usr/bin/env python3
"""
直接集成的实时录制器
不依赖MCP，直接与StudentsCMSSP系统集成
"""

import asyncio
import json
import logging
import threading
import time
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# 音频处理
import pyttsx3
import pyaudio
import wave

# 视频录制
import cv2
import numpy as np
from PIL import ImageGrab

# 浏览器控制
from playwright.async_api import async_playwright, Page, Browser

class DirectIntegrationRecorder:
    """直接集成的实时录制器"""
    
    def __init__(self, config_file: str = "config/recording_config.json"):
        self.config = self._load_config(config_file)
        self.logger = self._setup_logger()
        
        # 录制状态
        self.is_recording = False
        self.start_time = None
        
        # 组件
        self.tts_engine = None
        self.browser = None
        self.page = None
        self.video_writer = None
        
        # StudentsCMSSP API基础URL
        self.api_base = "http://127.0.0.1:8080"
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "recording": {
                "fps": 30,
                "resolution": [1920, 1080],
                "output_dir": "output/realtime/"
            },
            "tts": {
                "engine": "local",
                "rate": 150,
                "volume": 0.9
            },
            "browser": {
                "headless": False,
                "slow_mo": 1000,
                "viewport": {"width": 1920, "height": 1080}
            },
            "studentscms": {
                "base_url": "http://127.0.0.1:8080",
                "api_endpoints": {
                    "suppliers": "/api/suppliers",
                    "recipes": "/api/recipes",
                    "status": "/api/status"
                }
            }
        }
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # 合并配置
                default_config.update(user_config)
                return default_config
        except FileNotFoundError:
            return default_config
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('DirectRecorder')
        logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    async def initialize(self):
        """初始化所有组件"""
        self.logger.info("初始化实时录制器...")
        
        # 检查StudentsCMSSP系统
        if not self._check_studentscms():
            raise Exception("StudentsCMSSP系统未运行")
        
        # 初始化TTS
        self._init_tts()
        
        # 初始化浏览器
        await self._init_browser()
        
        # 初始化视频录制
        self._init_video_recorder()
        
        self.logger.info("初始化完成")
    
    def _check_studentscms(self) -> bool:
        """检查StudentsCMSSP系统状态"""
        try:
            response = requests.get(self.api_base, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _init_tts(self):
        """初始化TTS引擎"""
        self.tts_engine = pyttsx3.init()
        self.tts_engine.setProperty('rate', self.config["tts"]["rate"])
        self.tts_engine.setProperty('volume', self.config["tts"]["volume"])
        
        # 设置中文语音
        voices = self.tts_engine.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                self.tts_engine.setProperty('voice', voice.id)
                break
    
    async def _init_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=self.config["browser"]["headless"],
            slow_mo=self.config["browser"]["slow_mo"],
            args=['--start-maximized']
        )
        
        context = await self.browser.new_context(
            viewport=self.config["browser"]["viewport"]
        )
        
        self.page = await context.new_page()
    
    def _init_video_recorder(self):
        """初始化视频录制"""
        fps = self.config["recording"]["fps"]
        resolution = tuple(self.config["recording"]["resolution"])
        
        # 创建输出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(self.config["recording"]["output_dir"])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.output_file = output_dir / f"recording_{timestamp}.mp4"
        
        # 创建视频编写器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.video_writer = cv2.VideoWriter(
            str(self.output_file),
            fourcc,
            fps,
            resolution
        )
    
    async def start_recording(self, script_data: Dict[str, Any]):
        """开始录制"""
        self.logger.info("开始实时录制...")
        
        self.is_recording = True
        self.start_time = time.time()
        
        # 启动视频录制线程
        video_thread = threading.Thread(target=self._video_recording_loop)
        video_thread.daemon = True
        video_thread.start()
        
        try:
            # 执行录制脚本
            await self._execute_script(script_data)
        finally:
            await self._stop_recording()
    
    def _video_recording_loop(self):
        """视频录制循环"""
        fps = self.config["recording"]["fps"]
        resolution = tuple(self.config["recording"]["resolution"])
        
        while self.is_recording:
            try:
                # 截取屏幕
                screenshot = ImageGrab.grab()
                frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                frame = cv2.resize(frame, resolution)
                
                # 写入视频帧
                self.video_writer.write(frame)
                
                # 控制帧率
                time.sleep(1.0 / fps)
                
            except Exception as e:
                self.logger.error(f"视频录制错误: {e}")
                break
    
    async def _execute_script(self, script_data: Dict[str, Any]):
        """执行录制脚本"""
        scenes = script_data.get('scenes', [])
        
        for i, scene in enumerate(scenes):
            self.logger.info(f"执行场景 {i+1}/{len(scenes)}: {scene.get('title', 'Unknown')}")
            
            try:
                # 导航到页面
                if scene.get('url'):
                    await self.page.goto(scene['url'])
                    await asyncio.sleep(2)
                
                # 执行解说和操作
                narration = scene.get('narration', '')
                actions = scene.get('actions', [])
                
                if narration:
                    await self._speak_and_act(narration, actions)
                
                # 场景间隔
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"执行场景失败: {e}")
                continue
    
    async def _speak_and_act(self, narration: str, actions: List[Dict[str, Any]]):
        """同时进行解说和操作"""
        # 启动TTS解说
        tts_thread = threading.Thread(
            target=self._speak_text,
            args=(narration,)
        )
        tts_thread.start()
        
        # 执行操作
        for action in actions:
            await self._execute_action(action)
            await asyncio.sleep(0.5)
        
        # 等待解说完成
        tts_thread.join()
    
    def _speak_text(self, text: str):
        """TTS解说"""
        try:
            # 清理文本
            clean_text = text.strip().replace('\n', ' ')
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
        except Exception as e:
            self.logger.error(f"TTS失败: {e}")
    
    async def _execute_action(self, action: Dict[str, Any]):
        """执行操作"""
        action_type = action.get('type', 'unknown')
        
        try:
            if action_type == 'click':
                selector = action.get('selector')
                if selector:
                    await self.page.click(selector)
                    
            elif action_type == 'type':
                selector = action.get('selector')
                text = action.get('text', '')
                if selector:
                    await self.page.fill(selector, text)
                    
            elif action_type == 'wait':
                duration = action.get('duration', 1)
                await asyncio.sleep(duration)
                
            elif action_type == 'scroll':
                await self.page.keyboard.press('PageDown')
                
            elif action_type == 'api_call':
                # 调用StudentsCMSSP API
                await self._call_studentscms_api(action)
                
        except Exception as e:
            self.logger.error(f"执行操作失败: {action_type}, {e}")
    
    async def _call_studentscms_api(self, action: Dict[str, Any]):
        """调用StudentsCMSSP API"""
        endpoint = action.get('endpoint')
        method = action.get('method', 'GET')
        data = action.get('data', {})
        
        if not endpoint:
            return
        
        try:
            url = f"{self.api_base}{endpoint}"
            
            if method.upper() == 'GET':
                response = requests.get(url, params=data, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, timeout=10)
            else:
                return
            
            if response.status_code == 200:
                self.logger.info(f"API调用成功: {endpoint}")
            else:
                self.logger.warning(f"API调用失败: {endpoint}, {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"API调用错误: {e}")
    
    async def _stop_recording(self):
        """停止录制"""
        self.logger.info("停止录制...")
        
        self.is_recording = False
        
        # 释放资源
        if self.video_writer:
            self.video_writer.release()
        
        if self.browser:
            await self.browser.close()
        
        self.logger.info(f"录制完成: {self.output_file}")

# 使用示例
async def main():
    """示例用法"""
    # 简单的录制脚本
    script = {
        "title": "供应商模块演示",
        "scenes": [
            {
                "title": "系统首页",
                "url": "http://127.0.0.1:8080",
                "narration": "欢迎来到StudentsCMSSP学校食堂管理系统。",
                "actions": [
                    {"type": "wait", "duration": 3}
                ]
            },
            {
                "title": "供应商管理",
                "url": "http://127.0.0.1:8080/supplier/",
                "narration": "这里是供应商管理模块，我们可以看到所有供应商的信息。",
                "actions": [
                    {"type": "wait", "duration": 2},
                    {"type": "scroll"},
                    {"type": "wait", "duration": 2}
                ]
            }
        ]
    }
    
    recorder = DirectIntegrationRecorder()
    
    try:
        await recorder.initialize()
        await recorder.start_recording(script)
        print("录制完成！")
        
    except Exception as e:
        print(f"录制失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
