"""
录制脚本解析器
解析Markdown格式的录制脚本，提取场景信息、解说词和操作步骤
"""

import re
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

class ScriptParser:
    """录制脚本解析器"""
    
    def __init__(self, script_file: str):
        self.script_file = script_file
        self.scenes = []
    
    def parse_markdown_script(self) -> List[Dict[str, Any]]:
        """解析Markdown格式的录制脚本"""
        if not Path(self.script_file).exists():
            raise FileNotFoundError(f"脚本文件不存在: {self.script_file}")
        
        with open(self.script_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析各个部分
        scenes = self._extract_scenes(content)
        
        # 处理每个场景
        for scene in scenes:
            scene['actions'] = self._parse_actions(scene.get('content', ''))
            scene['narration'] = self._extract_narration(scene.get('content', ''))
            scene['url'] = self._extract_url(scene.get('content', ''))
            scene['technical_points'] = self._extract_technical_points(scene.get('content', ''))
        
        return scenes
    
    def _extract_scenes(self, content: str) -> List[Dict[str, Any]]:
        """提取所有场景"""
        scenes = []
        
        # 匹配场景标题和时间段
        scene_pattern = r'## (.+?) \((\d+:\d+)-(\d+:\d+)\)(.*?)(?=## |$)'
        matches = re.findall(scene_pattern, content, re.DOTALL)
        
        for i, (title, start_time, end_time, scene_content) in enumerate(matches):
            scene = {
                'id': i + 1,
                'title': title.strip(),
                'start_time': start_time,
                'end_time': end_time,
                'duration': self._calculate_duration(start_time, end_time),
                'content': scene_content.strip()
            }
            scenes.append(scene)
        
        return scenes
    
    def _calculate_duration(self, start_time: str, end_time: str) -> float:
        """计算场景持续时间（秒）"""
        def time_to_seconds(time_str: str) -> float:
            parts = time_str.split(':')
            return float(parts[0]) * 60 + float(parts[1])
        
        start_seconds = time_to_seconds(start_time)
        end_seconds = time_to_seconds(end_time)
        return end_seconds - start_seconds
    
    def _extract_narration(self, content: str) -> str:
        """提取解说词"""
        # 匹配解说词部分
        narration_patterns = [
            r'### 解说词\s*\n"(.+?)"',
            r'### 解说词\s*\n(.+?)(?=\n###|\n---|\Z)',
            r'"(.+?)"'  # 简单的引号内容
        ]
        
        for pattern in narration_patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                narration = match.group(1).strip()
                # 清理格式
                narration = re.sub(r'\n+', ' ', narration)
                narration = re.sub(r'\s+', ' ', narration)
                return narration
        
        return ""
    
    def _extract_url(self, content: str) -> Optional[str]:
        """提取访问路径"""
        url_patterns = [
            r'### 访问路径\s*\n`(.+?)`',
            r'访问路径[：:]\s*`(.+?)`',
            r'URL[：:]?\s*`(.+?)`',
            r'http://127\.0\.0\.1:8080[^\s]*'
        ]
        
        for pattern in url_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1) if match.groups() else match.group(0)
        
        return None
    
    def _extract_technical_points(self, content: str) -> List[str]:
        """提取技术亮点"""
        points = []
        
        # 匹配技术亮点部分
        tech_pattern = r'### 技术亮点解说\s*\n"(.+?)"'
        match = re.search(tech_pattern, content, re.DOTALL)
        
        if match:
            tech_content = match.group(1)
            # 提取列表项
            point_pattern = r'\d+\.\s*\*\*(.+?)\*\*[：:](.+?)(?=\n\d+\.|\Z)'
            point_matches = re.findall(point_pattern, tech_content, re.DOTALL)
            
            for title, description in point_matches:
                points.append({
                    'title': title.strip(),
                    'description': description.strip()
                })
        
        return points
    
    def _parse_actions(self, content: str) -> List[Dict[str, Any]]:
        """解析操作步骤"""
        actions = []
        
        # 匹配录制步骤部分
        steps_pattern = r'### 录制步骤\s*\n(.+?)(?=\n###|\n---|\Z)'
        steps_match = re.search(steps_pattern, content, re.DOTALL)
        
        if not steps_match:
            return actions
        
        steps_content = steps_match.group(1)
        
        # 匹配每个操作步骤
        action_pattern = r'\d+\.\s*\*\*(.+?)\*\*\s*\n(.+?)(?=\n\d+\.|\Z)'
        action_matches = re.findall(action_pattern, steps_content, re.DOTALL)
        
        for title, description in action_matches:
            action = {
                'title': title.strip(),
                'description': description.strip(),
                'steps': self._parse_detailed_steps(description)
            }
            actions.append(action)
        
        return actions
    
    def _parse_detailed_steps(self, description: str) -> List[Dict[str, Any]]:
        """解析详细操作步骤"""
        steps = []
        
        # 匹配代码块中的操作步骤
        code_block_pattern = r'```\s*\n(.+?)\n\s*```'
        code_matches = re.findall(code_block_pattern, description, re.DOTALL)
        
        for code_block in code_matches:
            # 解析操作步骤
            step_lines = code_block.strip().split('\n')
            current_step = None
            
            for line in step_lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是新的步骤
                step_match = re.match(r'(\d+)\.\s*(.+)', line)
                if step_match:
                    if current_step:
                        steps.append(current_step)
                    
                    current_step = {
                        'order': int(step_match.group(1)),
                        'action': self._determine_action_type(step_match.group(2)),
                        'description': step_match.group(2),
                        'target': self._extract_target(step_match.group(2)),
                        'value': self._extract_value(step_match.group(2)),
                        'wait_time': self._extract_wait_time(step_match.group(2))
                    }
                else:
                    # 继续描述或参数
                    if current_step and line.startswith('-'):
                        param_match = re.match(r'-\s*(.+?)[：:](.+)', line)
                        if param_match:
                            if 'parameters' not in current_step:
                                current_step['parameters'] = {}
                            current_step['parameters'][param_match.group(1).strip()] = param_match.group(2).strip()
            
            if current_step:
                steps.append(current_step)
        
        # 如果没有代码块，解析普通列表
        if not steps:
            list_pattern = r'-\s*(.+?)(?=\n-|\Z)'
            list_matches = re.findall(list_pattern, description, re.DOTALL)
            
            for i, item in enumerate(list_matches):
                step = {
                    'order': i + 1,
                    'action': self._determine_action_type(item),
                    'description': item.strip(),
                    'target': self._extract_target(item),
                    'value': self._extract_value(item),
                    'wait_time': self._extract_wait_time(item)
                }
                steps.append(step)
        
        return steps
    
    def _determine_action_type(self, description: str) -> str:
        """确定操作类型"""
        description_lower = description.lower()
        
        if any(keyword in description_lower for keyword in ['点击', 'click']):
            return 'click'
        elif any(keyword in description_lower for keyword in ['输入', '填写', 'type', 'input']):
            return 'type'
        elif any(keyword in description_lower for keyword in ['选择', 'select']):
            return 'select'
        elif any(keyword in description_lower for keyword in ['等待', 'wait']):
            return 'wait'
        elif any(keyword in description_lower for keyword in ['展示', '显示', 'show', 'display']):
            return 'display'
        elif any(keyword in description_lower for keyword in ['导航', '访问', 'navigate', 'goto']):
            return 'navigate'
        elif any(keyword in description_lower for keyword in ['滚动', 'scroll']):
            return 'scroll'
        elif any(keyword in description_lower for keyword in ['悬停', 'hover']):
            return 'hover'
        else:
            return 'custom'
    
    def _extract_target(self, description: str) -> Optional[str]:
        """提取操作目标（选择器）"""
        # 匹配常见的选择器模式
        selector_patterns = [
            r'#([a-zA-Z][\w-]*)',  # ID选择器
            r'\.([a-zA-Z][\w-]*)',  # 类选择器
            r'\[(.+?)\]',  # 属性选择器
            r'"(.+?)"',  # 引号内的文本
            r'`(.+?)`'   # 反引号内的选择器
        ]
        
        for pattern in selector_patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1) if pattern.startswith(r'\[') else match.group(0)
        
        return None
    
    def _extract_value(self, description: str) -> Optional[str]:
        """提取输入值"""
        # 匹配输入值模式
        value_patterns = [
            r'输入[：:](.+?)(?=\n|$)',
            r'填写[：:](.+?)(?=\n|$)',
            r'值[：:](.+?)(?=\n|$)'
        ]
        
        for pattern in value_patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_wait_time(self, description: str) -> float:
        """提取等待时间"""
        wait_patterns = [
            r'等待\s*(\d+(?:\.\d+)?)\s*秒',
            r'(\d+(?:\.\d+)?)\s*秒',
            r'wait\s*(\d+(?:\.\d+)?)'
        ]
        
        for pattern in wait_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return float(match.group(1))
        
        return 1.0  # 默认等待时间
    
    def export_to_json(self, output_file: str):
        """导出解析结果为JSON格式"""
        scenes = self.parse_markdown_script()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(scenes, f, ensure_ascii=False, indent=2)
    
    def validate_script(self) -> List[str]:
        """验证脚本完整性"""
        errors = []
        scenes = self.parse_markdown_script()
        
        for i, scene in enumerate(scenes):
            scene_num = i + 1
            
            if not scene.get('title'):
                errors.append(f"场景 {scene_num}: 缺少标题")
            
            if not scene.get('narration'):
                errors.append(f"场景 {scene_num}: 缺少解说词")
            
            if not scene.get('actions'):
                errors.append(f"场景 {scene_num}: 缺少操作步骤")
            
            if scene.get('duration', 0) <= 0:
                errors.append(f"场景 {scene_num}: 时间段设置错误")
        
        return errors

if __name__ == "__main__":
    # 测试脚本解析器
    parser = ScriptParser("../食谱管理模块视频录制脚本.md")
    
    try:
        scenes = parser.parse_markdown_script()
        print(f"解析到 {len(scenes)} 个场景:")
        
        for scene in scenes:
            print(f"\n场景: {scene['title']}")
            print(f"时长: {scene['duration']} 秒")
            print(f"操作步骤: {len(scene['actions'])} 个")
            print(f"解说词长度: {len(scene['narration'])} 字符")
        
        # 验证脚本
        errors = parser.validate_script()
        if errors:
            print("\n发现以下问题:")
            for error in errors:
                print(f"- {error}")
        else:
            print("\n✅ 脚本验证通过")
            
    except Exception as e:
        print(f"解析失败: {e}")
