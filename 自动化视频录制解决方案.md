# StudentsCMSSP 自动化视频录制解决方案

## 方案概述

基于现有的食谱管理模块视频录制脚本，我们可以实现全自动的视频录制、音频合成和后期制作。该方案结合了多种技术栈，实现从脚本到成品视频的完全自动化流程。

## 技术架构

### 1. 核心技术栈
- **浏览器自动化**: Playwright (Python)
- **屏幕录制**: FFmpeg + Python
- **音频合成**: Azure TTS / Google TTS / 本地TTS
- **视频编辑**: MoviePy (Python)
- **图像处理**: Pillow + OpenCV
- **脚本解析**: Python + JSON

### 2. 系统架构图
```
录制脚本 → 脚本解析器 → 浏览器控制器 → 屏幕录制器
    ↓           ↓            ↓            ↓
音频脚本 → TTS引擎 → 音频处理器 → 视频合成器 → 成品视频
```

## 实现方案

### 方案一：基于Playwright的全自动录制

#### 1. 项目结构
```
auto_video_recorder/
├── config/
│   ├── recording_config.json
│   └── tts_config.json
├── scripts/
│   ├── recipe_script.json
│   └── script_parser.py
├── recorder/
│   ├── browser_controller.py
│   ├── screen_recorder.py
│   └── audio_generator.py
├── editor/
│   ├── video_editor.py
│   └── subtitle_generator.py
├── utils/
│   ├── file_manager.py
│   └── logger.py
└── main.py
```

#### 2. 核心实现代码

##### 脚本解析器 (scripts/script_parser.py)
```python
import json
import re
from typing import List, Dict, Any

class ScriptParser:
    def __init__(self, script_file: str):
        self.script_file = script_file
        self.scenes = []
        
    def parse_markdown_script(self) -> List[Dict[str, Any]]:
        """解析Markdown格式的录制脚本"""
        with open(self.script_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析时间段
        time_pattern = r'## (.+?) \((\d+:\d+)-(\d+:\d+)\)'
        sections = re.split(time_pattern, content)
        
        scenes = []
        for i in range(1, len(sections), 4):
            title = sections[i]
            start_time = sections[i+1]
            end_time = sections[i+2]
            content = sections[i+3]
            
            # 解析解说词
            narration = self._extract_narration(content)
            
            # 解析操作步骤
            actions = self._extract_actions(content)
            
            # 解析访问路径
            url = self._extract_url(content)
            
            scene = {
                'title': title,
                'start_time': start_time,
                'end_time': end_time,
                'duration': self._calculate_duration(start_time, end_time),
                'narration': narration,
                'actions': actions,
                'url': url
            }
            scenes.append(scene)
        
        return scenes
    
    def _extract_narration(self, content: str) -> str:
        """提取解说词"""
        narration_pattern = r'### 解说词\n"(.+?)"'
        match = re.search(narration_pattern, content, re.DOTALL)
        return match.group(1) if match else ""
    
    def _extract_actions(self, content: str) -> List[Dict[str, Any]]:
        """提取操作步骤"""
        actions = []
        action_pattern = r'\d+\. \*\*(.+?)\*\*\n(.+?)(?=\n\d+\.|\n###|\Z)'
        matches = re.findall(action_pattern, content, re.DOTALL)
        
        for title, description in matches:
            action = {
                'title': title,
                'description': description.strip(),
                'steps': self._parse_steps(description)
            }
            actions.append(action)
        
        return actions
    
    def _parse_steps(self, description: str) -> List[Dict[str, str]]:
        """解析具体操作步骤"""
        steps = []
        step_pattern = r'- (.+?)(?=\n-|\Z)'
        matches = re.findall(step_pattern, description, re.DOTALL)
        
        for step in matches:
            step_info = {
                'action': self._determine_action_type(step),
                'target': self._extract_target(step),
                'value': self._extract_value(step),
                'description': step.strip()
            }
            steps.append(step_info)
        
        return steps
    
    def _determine_action_type(self, step: str) -> str:
        """确定操作类型"""
        if '点击' in step:
            return 'click'
        elif '输入' in step or '填写' in step:
            return 'type'
        elif '选择' in step:
            return 'select'
        elif '展示' in step or '显示' in step:
            return 'display'
        elif '等待' in step:
            return 'wait'
        else:
            return 'custom'
```

##### 浏览器控制器 (recorder/browser_controller.py)
```python
from playwright.async_api import async_playwright, Page, Browser
import asyncio
import time
from typing import Dict, List, Any

class BrowserController:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.browser: Browser = None
        self.page: Page = None
        
    async def initialize(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,
            args=[
                '--start-maximized',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            record_video_dir='recordings/raw_videos/'
        )
        
        self.page = await context.new_page()
        
    async def execute_scene(self, scene: Dict[str, Any]) -> str:
        """执行场景录制"""
        print(f"开始录制场景: {scene['title']}")
        
        # 导航到指定URL
        if scene.get('url'):
            await self.page.goto(scene['url'])
            await self.page.wait_for_load_state('networkidle')
        
        # 执行操作步骤
        for action in scene['actions']:
            await self._execute_action(action)
            
        # 等待场景完成
        await asyncio.sleep(2)
        
        return f"recordings/raw_videos/scene_{scene['title']}.webm"
    
    async def _execute_action(self, action: Dict[str, Any]):
        """执行具体操作"""
        for step in action['steps']:
            await self._execute_step(step)
            await asyncio.sleep(1)  # 操作间隔
    
    async def _execute_step(self, step: Dict[str, str]):
        """执行单个步骤"""
        action_type = step['action']
        target = step['target']
        value = step.get('value', '')
        
        try:
            if action_type == 'click':
                await self.page.click(target)
            elif action_type == 'type':
                await self.page.fill(target, value)
            elif action_type == 'select':
                await self.page.select_option(target, value)
            elif action_type == 'wait':
                await asyncio.sleep(float(value))
            elif action_type == 'display':
                # 高亮显示元素
                await self._highlight_element(target)
                
        except Exception as e:
            print(f"执行步骤失败: {step['description']}, 错误: {e}")
    
    async def _highlight_element(self, selector: str):
        """高亮显示元素"""
        await self.page.evaluate(f"""
            const element = document.querySelector('{selector}');
            if (element) {{
                element.style.border = '3px solid red';
                element.style.backgroundColor = 'yellow';
                setTimeout(() => {{
                    element.style.border = '';
                    element.style.backgroundColor = '';
                }}, 2000);
            }}
        """)
```

##### 音频生成器 (recorder/audio_generator.py)
```python
import azure.cognitiveservices.speech as speechsdk
from gtts import gTTS
import pyttsx3
import os
from typing import Dict, Any

class AudioGenerator:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.tts_engine = config.get('tts_engine', 'azure')
        
    def generate_audio(self, text: str, output_file: str) -> str:
        """生成音频文件"""
        if self.tts_engine == 'azure':
            return self._generate_azure_tts(text, output_file)
        elif self.tts_engine == 'google':
            return self._generate_google_tts(text, output_file)
        else:
            return self._generate_local_tts(text, output_file)
    
    def _generate_azure_tts(self, text: str, output_file: str) -> str:
        """使用Azure TTS生成音频"""
        speech_config = speechsdk.SpeechConfig(
            subscription=self.config['azure_key'],
            region=self.config['azure_region']
        )
        
        # 设置中文语音
        speech_config.speech_synthesis_voice_name = "zh-CN-XiaoxiaoNeural"
        
        audio_config = speechsdk.audio.AudioOutputConfig(filename=output_file)
        synthesizer = speechsdk.SpeechSynthesizer(
            speech_config=speech_config,
            audio_config=audio_config
        )
        
        # 添加SSML标记以控制语音效果
        ssml_text = f"""
        <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
            <voice name="zh-CN-XiaoxiaoNeural">
                <prosody rate="0.9" pitch="medium">
                    {text}
                </prosody>
            </voice>
        </speak>
        """
        
        result = synthesizer.speak_ssml_async(ssml_text).get()
        
        if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
            return output_file
        else:
            raise Exception(f"TTS生成失败: {result.reason}")
    
    def _generate_google_tts(self, text: str, output_file: str) -> str:
        """使用Google TTS生成音频"""
        tts = gTTS(text=text, lang='zh', slow=False)
        tts.save(output_file)
        return output_file
    
    def _generate_local_tts(self, text: str, output_file: str) -> str:
        """使用本地TTS生成音频"""
        engine = pyttsx3.init()
        engine.setProperty('rate', 150)  # 语速
        engine.setProperty('volume', 0.9)  # 音量
        
        # 设置中文语音（如果可用）
        voices = engine.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                break
        
        engine.save_to_file(text, output_file)
        engine.runAndWait()
        return output_file
```

##### 视频编辑器 (editor/video_editor.py)
```python
from moviepy.editor import *
import os
from typing import List, Dict, Any

class VideoEditor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
    def create_final_video(self, scenes: List[Dict[str, Any]]) -> str:
        """创建最终视频"""
        video_clips = []
        
        for i, scene in enumerate(scenes):
            # 加载视频片段
            video_file = f"recordings/raw_videos/scene_{i}.webm"
            audio_file = f"recordings/audio/scene_{i}.wav"
            
            if os.path.exists(video_file) and os.path.exists(audio_file):
                # 加载视频和音频
                video_clip = VideoFileClip(video_file)
                audio_clip = AudioFileClip(audio_file)
                
                # 调整视频长度匹配音频
                if video_clip.duration < audio_clip.duration:
                    # 视频太短，循环播放最后一帧
                    last_frame = video_clip.subclip(-0.1)
                    extension = last_frame.loop(duration=audio_clip.duration - video_clip.duration)
                    video_clip = concatenate_videoclips([video_clip, extension])
                else:
                    # 视频太长，截取匹配音频长度
                    video_clip = video_clip.subclip(0, audio_clip.duration)
                
                # 合并音视频
                final_clip = video_clip.set_audio(audio_clip)
                
                # 添加标题字幕
                title_clip = self._create_title_clip(scene['title'], final_clip.duration)
                final_clip = CompositeVideoClip([final_clip, title_clip])
                
                video_clips.append(final_clip)
        
        # 合并所有片段
        final_video = concatenate_videoclips(video_clips)
        
        # 添加片头片尾
        intro_clip = self._create_intro_clip()
        outro_clip = self._create_outro_clip()
        
        complete_video = concatenate_videoclips([intro_clip, final_video, outro_clip])
        
        # 输出最终视频
        output_file = "output/StudentsCMSSP_食谱管理模块演示.mp4"
        complete_video.write_videofile(
            output_file,
            fps=30,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        return output_file
    
    def _create_title_clip(self, title: str, duration: float) -> TextClip:
        """创建标题字幕"""
        return TextClip(
            title,
            fontsize=24,
            color='white',
            font='Arial-Bold'
        ).set_position(('center', 50)).set_duration(3)
    
    def _create_intro_clip(self) -> VideoClip:
        """创建片头"""
        # 创建简单的片头
        intro_text = TextClip(
            "StudentsCMSSP\n学校食堂管理系统\n食谱管理模块演示",
            fontsize=48,
            color='white',
            font='Arial-Bold'
        ).set_duration(3).set_position('center')
        
        intro_bg = ColorClip(size=(1920, 1080), color=(0, 123, 255)).set_duration(3)
        
        return CompositeVideoClip([intro_bg, intro_text])
    
    def _create_outro_clip(self) -> VideoClip:
        """创建片尾"""
        outro_text = TextClip(
            "感谢观看\n如有疑问请联系技术支持\nwww.studentscms.com",
            fontsize=36,
            color='white',
            font='Arial-Bold'
        ).set_duration(3).set_position('center')
        
        outro_bg = ColorClip(size=(1920, 1080), color=(0, 123, 255)).set_duration(3)
        
        return CompositeVideoClip([outro_bg, outro_text])
```

##### 主控制器 (main.py)
```python
import asyncio
import json
import os
from scripts.script_parser import ScriptParser
from recorder.browser_controller import BrowserController
from recorder.audio_generator import AudioGenerator
from editor.video_editor import VideoEditor

class AutoVideoRecorder:
    def __init__(self, config_file: str):
        with open(config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.script_parser = ScriptParser(self.config['script_file'])
        self.browser_controller = BrowserController(self.config['browser'])
        self.audio_generator = AudioGenerator(self.config['tts'])
        self.video_editor = VideoEditor(self.config['video'])
    
    async def record_video(self):
        """录制完整视频"""
        print("开始自动化视频录制...")
        
        # 1. 解析脚本
        scenes = self.script_parser.parse_markdown_script()
        print(f"解析到 {len(scenes)} 个场景")
        
        # 2. 初始化浏览器
        await self.browser_controller.initialize()
        
        # 3. 生成音频文件
        print("生成音频文件...")
        for i, scene in enumerate(scenes):
            audio_file = f"recordings/audio/scene_{i}.wav"
            os.makedirs(os.path.dirname(audio_file), exist_ok=True)
            self.audio_generator.generate_audio(scene['narration'], audio_file)
        
        # 4. 录制视频片段
        print("录制视频片段...")
        for i, scene in enumerate(scenes):
            await self.browser_controller.execute_scene(scene)
        
        # 5. 合成最终视频
        print("合成最终视频...")
        final_video = self.video_editor.create_final_video(scenes)
        
        print(f"视频录制完成: {final_video}")
        
        # 6. 清理临时文件
        await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        if self.browser_controller.browser:
            await self.browser_controller.browser.close()

async def main():
    recorder = AutoVideoRecorder('config/recording_config.json')
    await recorder.record_video()

if __name__ == "__main__":
    asyncio.run(main())
```

### 方案二：基于Selenium + OBS的录制方案

#### 核心特点
- 使用Selenium控制浏览器
- 使用OBS Studio进行屏幕录制
- 通过OBS WebSocket API控制录制
- 支持更复杂的录制场景

#### 实现要点
```python
import obsws_python as obs
from selenium import webdriver
from selenium.webdriver.common.by import By
import time

class OBSRecorder:
    def __init__(self):
        self.obs_client = obs.ReqClient(host='localhost', port=4455, password='your_password')
        
    def start_recording(self, scene_name: str):
        """开始录制指定场景"""
        self.obs_client.set_current_program_scene(scene_name)
        self.obs_client.start_record()
        
    def stop_recording(self):
        """停止录制"""
        self.obs_client.stop_record()
```

### 方案三：云端自动化录制

#### 技术架构
- 使用Docker容器化部署
- 在云服务器上运行录制脚本
- 支持分布式录制和处理
- 自动上传到云存储

## 配置文件示例

### recording_config.json
```json
{
  "script_file": "食谱管理模块视频录制脚本.md",
  "browser": {
    "headless": false,
    "viewport": {
      "width": 1920,
      "height": 1080
    },
    "slow_mo": 1000
  },
  "tts": {
    "engine": "azure",
    "azure_key": "your_azure_key",
    "azure_region": "eastasia",
    "voice": "zh-CN-XiaoxiaoNeural",
    "rate": "0.9",
    "pitch": "medium"
  },
  "video": {
    "fps": 30,
    "quality": "high",
    "format": "mp4",
    "resolution": "1920x1080"
  },
  "output": {
    "directory": "output/",
    "filename": "StudentsCMSSP_食谱管理模块演示_{timestamp}.mp4"
  }
}
```

## 部署和使用

### 1. 环境准备
```bash
# 安装Python依赖
pip install playwright moviepy azure-cognitiveservices-speech gtts pyttsx3

# 安装Playwright浏览器
playwright install chromium

# 安装FFmpeg
# Windows: 下载FFmpeg并添加到PATH
# Linux: sudo apt install ffmpeg
# macOS: brew install ffmpeg
```

### 2. 运行录制
```bash
python main.py
```

### 3. 自动化部署
```dockerfile
FROM python:3.9

RUN apt-get update && apt-get install -y \
    ffmpeg \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt
RUN playwright install chromium

COPY . /app
WORKDIR /app

CMD ["python", "main.py"]
```

## 优势和特点

1. **完全自动化**: 从脚本到成品视频的全自动流程
2. **高质量输出**: 支持1080p高清录制和专业音频合成
3. **灵活配置**: 支持多种TTS引擎和录制参数
4. **可扩展性**: 易于添加新的录制场景和功能
5. **成本效益**: 一次开发，多次使用，大幅降低视频制作成本

这套方案可以将您的30分钟视频录制工作从人工操作变为完全自动化，大大提高效率和一致性。
