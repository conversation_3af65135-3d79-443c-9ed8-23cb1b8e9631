# StudentsCMSSP 自动化视频录制器 - 最小依赖包
# 用于快速启动和基本功能

# 核心依赖
playwright>=1.40.0
requests>=2.31.0

# 本地TTS（Windows内置）
pyttsx3>=2.90

# 基础视频处理（可选，用于高级功能）
# moviepy>=1.0.3
# opencv-python>=********
# pillow>=10.1.0

# 系统工具
psutil>=5.9.6

# 进度条
tqdm>=4.66.1

# 注意：
# 1. winsound 是 Python 标准库，无需安装
# 2. 如需高级视频编辑功能，请安装完整版依赖：pip install -r requirements.txt
# 3. 如需Azure TTS，请安装：pip install azure-cognitiveservices-speech
