{"mcpServers": {"AllVoiceLab": {"command": "uvx", "args": ["allvoicelab-mcp", "-y"], "env": {"ALLVOICELAB_API_KEY": "your_allvoicelab_api_key_here", "ALLVOICELAB_API_DOMAIN": "https://api.allvoicelab.cn", "ALLVOICELAB_BASE_PATH": "c:/StudentsCMSSP/output"}}, "MiniMax": {"command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "MINIMAX_API_HOST": "https://api.minimaxi.com", "MINIMAX_GROUP_ID": "1936249813653791548", "MINIMAX_BASE_PATH": "c:/StudentsCMSSP/output"}}, "StudentsCMSSP": {"command": "c:/StudentsCMSSP/mcp_env/Scripts/python", "args": ["-m", "studentscms_mcp_server"], "env": {"STUDENTSCMS_BASE_URL": "http://127.0.0.1:8080", "STUDENTSCMS_BASE_PATH": "c:/StudentsCMSSP"}}}}