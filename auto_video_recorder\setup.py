#!/usr/bin/env python3
"""
StudentsCMSSP 自动化视频录制器安装脚本
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    else:
        print(f"✅ Python版本检查通过: {sys.version}")

def install_system_dependencies():
    """安装系统依赖"""
    system = platform.system().lower()
    
    print(f"检测到系统: {system}")
    
    if system == "windows":
        print("Windows系统，请确保已安装以下软件:")
        print("- FFmpeg (https://ffmpeg.org/download.html)")
        print("- Microsoft Visual C++ Redistributable")
        
    elif system == "darwin":  # macOS
        print("安装macOS依赖...")
        try:
            subprocess.run(["brew", "install", "ffmpeg"], check=True)
            print("✅ FFmpeg安装完成")
        except subprocess.CalledProcessError:
            print("❌ 请先安装Homebrew，然后运行: brew install ffmpeg")
            
    elif system == "linux":
        print("安装Linux依赖...")
        try:
            # 尝试使用apt-get (Ubuntu/Debian)
            subprocess.run([
                "sudo", "apt-get", "update", "&&",
                "sudo", "apt-get", "install", "-y",
                "ffmpeg", "xvfb", "python3-dev", "python3-pip"
            ], shell=True, check=True)
            print("✅ 系统依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 请手动安装: ffmpeg, xvfb, python3-dev")

def install_python_dependencies():
    """安装Python依赖包"""
    print("安装Python依赖包...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True)
        
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        
        print("✅ Python依赖包安装完成")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Python依赖包失败: {e}")
        sys.exit(1)

def install_playwright_browsers():
    """安装Playwright浏览器"""
    print("安装Playwright浏览器...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], check=True)
        
        print("✅ Playwright浏览器安装完成")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Playwright浏览器失败: {e}")
        sys.exit(1)

def create_directories():
    """创建必要的目录结构"""
    print("创建目录结构...")
    
    directories = [
        "recordings/raw_videos",
        "recordings/audio", 
        "recordings/screenshots",
        "output/temp",
        "output/final",
        "logs",
        "assets"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")

def setup_config():
    """设置配置文件"""
    print("设置配置文件...")
    
    config_file = "config/recording_config.json"
    example_file = "config/recording_config.example.json"
    
    if not os.path.exists(config_file):
        if os.path.exists(example_file):
            import shutil
            shutil.copy(example_file, config_file)
            print(f"✅ 配置文件已创建: {config_file}")
        else:
            print(f"⚠️  请手动创建配置文件: {config_file}")
    else:
        print(f"✅ 配置文件已存在: {config_file}")

def test_installation():
    """测试安装"""
    print("测试安装...")
    
    try:
        # 测试导入主要模块
        import playwright
        import moviepy
        import cv2
        import PIL
        
        print("✅ 主要模块导入成功")
        
        # 测试Playwright
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        
        print("✅ Playwright测试通过")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def main():
    """主安装流程"""
    print("=" * 60)
    print("StudentsCMSSP 自动化视频录制器 - 安装程序")
    print("=" * 60)
    
    # 1. 检查Python版本
    check_python_version()
    
    # 2. 安装系统依赖
    install_system_dependencies()
    
    # 3. 安装Python依赖
    install_python_dependencies()
    
    # 4. 安装Playwright浏览器
    install_playwright_browsers()
    
    # 5. 创建目录结构
    create_directories()
    
    # 6. 设置配置文件
    setup_config()
    
    # 7. 测试安装
    if test_installation():
        print("\n" + "=" * 60)
        print("🎉 安装完成!")
        print("=" * 60)
        print("使用方法:")
        print("1. 编辑配置文件: config/recording_config.json")
        print("2. 准备录制脚本: 食谱管理模块视频录制脚本.md")
        print("3. 运行录制: python main.py")
        print("=" * 60)
    else:
        print("\n❌ 安装验证失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
