"""
安全监控模块
监控和记录可疑的网络活动
"""

import logging
import re
from datetime import datetime, timedelta
from collections import defaultdict
from flask import request, current_app

class SecurityMonitor:
    """安全监控器"""
    
    def __init__(self):
        self.suspicious_ips = defaultdict(list)
        self.blocked_ips = set()
        self.max_requests_per_minute = 200  # 增加到200次/分钟，与security_config.py保持一致
        self.block_duration = timedelta(minutes=30)

        # 白名单IP（用户的已知IP）
        self.whitelist_ips = {
            '***************',  # 用户机器IP
            '***************',  # 用户常用IP
            '127.0.0.1',        # 本地IP
            'localhost',        # 本地主机
            '::1',              # IPv6本地回环
            '0.0.0.0'           # 所有接口
        }

        # 白名单路径（不需要严格检查的路径）
        self.whitelist_paths = {
            '/notifications/check',
            '/financial/vouchers/batch-export',
            '/static/',
            '/favicon.ico',
            '/api/',               # API路径
            '/uploads/',           # 上传文件路径
            '/css/',               # CSS文件
            '/js/',                # JavaScript文件
            '/images/',            # 图片文件
            '/fonts/'              # 字体文件
        }
    
    def is_ssl_handshake_request(self, raw_data):
        """检测是否是SSL握手请求"""
        if not raw_data:
            return False
        
        # SSL握手通常以特定字节开始
        ssl_patterns = [
            b'\x16\x03',  # TLS握手
            b'\x15\x03',  # TLS警告
            b'\x14\x03',  # TLS更改密码规范
            b'\x17\x03',  # TLS应用数据
        ]
        
        raw_bytes = raw_data.encode('latin1') if isinstance(raw_data, str) else raw_data
        return any(raw_bytes.startswith(pattern) for pattern in ssl_patterns)
    
    def is_suspicious_request(self, request_obj):
        """检测可疑请求"""
        suspicious_indicators = []

        # 获取客户端IP - 使用与security_config.py相同的方法
        client_ip = request_obj.environ.get('HTTP_X_FORWARDED_FOR', request_obj.remote_addr)
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()

        # 检查是否在白名单中
        if client_ip in self.whitelist_ips:
            return []  # 白名单IP不检查

        # 检查路径是否在白名单中
        path = request_obj.path
        if any(path.startswith(whitelist_path) for whitelist_path in self.whitelist_paths):
            return []  # 白名单路径不检查

        # 检查User-Agent
        user_agent = request_obj.headers.get('User-Agent', '')
        if not user_agent:
            # 只对非静态资源和非API请求检查User-Agent
            if not any(path.endswith(ext) for ext in ['.css', '.js', '.png', '.jpg', '.ico', '.svg']) and \
               not path.startswith('/notifications/'):
                suspicious_indicators.append('无User-Agent')
        elif any(bot in user_agent.lower() for bot in ['bot', 'crawler', 'spider', 'scan']):
            # 排除合法的搜索引擎爬虫
            if not any(good_bot in user_agent.lower() for good_bot in ['googlebot', 'bingbot', 'baiduspider']):
                suspicious_indicators.append('可疑机器人User-Agent')

        # 检查请求路径
        path = request_obj.path
        suspicious_paths = [
            '/wp-admin', '/phpmyadmin', '/.env', '/config.php',
            '/xmlrpc.php', '/wp-login.php', '/.git', '/admin.php',
            '/manager/html', '/console', '/solr'
        ]
        if any(sus_path in path.lower() for sus_path in suspicious_paths):
            suspicious_indicators.append('可疑路径')

        # 检查请求方法
        if request_obj.method in ['TRACE', 'CONNECT'] and path != '/':
            suspicious_indicators.append('可疑HTTP方法')

        # 改进代理请求检测 - 更宽松的检测
        headers = request_obj.headers
        forwarded_for = headers.get('X-Forwarded-For', '')
        real_ip = headers.get('X-Real-IP', '')

        # 只有当X-Forwarded-For包含多个IP或者看起来像攻击时才标记
        if forwarded_for:
            # 检查是否包含多个IP（可能的代理链）
            ips = [ip.strip() for ip in forwarded_for.split(',')]
            if len(ips) > 2:  # 超过2个IP可能是可疑的代理链
                suspicious_indicators.append('复杂代理链')
            # 检查是否包含内网IP但来源是外网
            elif any(ip.startswith(('10.', '172.', '192.168.', '127.')) for ip in ips):
                remote_addr = request_obj.environ.get('REMOTE_ADDR', '')
                if not remote_addr.startswith(('10.', '172.', '192.168.', '127.')):
                    suspicious_indicators.append('可疑内网IP转发')

        return suspicious_indicators
    
    def log_security_event(self, event_type, details, ip_address=None, severity='WARNING'):
        """记录安全事件"""
        if not ip_address:
            ip_address = request.environ.get('REMOTE_ADDR', 'unknown')
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        log_message = f"[安全监控] {timestamp} - {event_type} | IP: {ip_address} | {details}"
        
        if severity == 'CRITICAL':
            current_app.logger.critical(log_message)
        elif severity == 'ERROR':
            current_app.logger.error(log_message)
        elif severity == 'WARNING':
            current_app.logger.warning(log_message)
        else:
            current_app.logger.info(log_message)
    
    def check_rate_limit(self, ip_address):
        """检查IP访问频率限制"""
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        
        # 清理过期记录
        self.suspicious_ips[ip_address] = [
            timestamp for timestamp in self.suspicious_ips[ip_address]
            if timestamp > minute_ago
        ]
        
        # 检查是否超过限制
        if len(self.suspicious_ips[ip_address]) >= self.max_requests_per_minute:
            self.blocked_ips.add(ip_address)
            self.log_security_event(
                '频率限制触发',
                f'IP {ip_address} 在1分钟内请求 {len(self.suspicious_ips[ip_address])} 次',
                ip_address,
                'ERROR'
            )
            return False
        
        # 记录当前请求
        self.suspicious_ips[ip_address].append(now)
        return True
    
    def is_blocked(self, ip_address):
        """检查IP是否被阻止"""
        return ip_address in self.blocked_ips
    
    def unblock_ip(self, ip_address):
        """解除IP阻止"""
        self.blocked_ips.discard(ip_address)
        self.suspicious_ips.pop(ip_address, None)

# 全局安全监控实例
security_monitor = SecurityMonitor()

def init_security_monitoring(app):
    """初始化安全监控"""
    
    @app.before_request
    def security_check():
        """请求前安全检查"""
        # 使用与security_config.py相同的IP检测方法
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip_address and ',' in ip_address:
            ip_address = ip_address.split(',')[0].strip()

        # 添加调试日志
        current_app.logger.info(f'[安全监控] 检测到IP: {ip_address}, 路径: {request.path}')

        # 首先检查是否在白名单中，如果是则跳过所有安全检查
        if ip_address in security_monitor.whitelist_ips:
            current_app.logger.info(f'[安全监控] IP {ip_address} 在白名单中，跳过安全检查')
            return

        # 检查静态资源路径，跳过安全检查
        if any(request.path.startswith(whitelist_path) for whitelist_path in security_monitor.whitelist_paths):
            current_app.logger.info(f'[安全监控] 路径 {request.path} 在白名单中，跳过安全检查')
            return

        # 检查是否被阻止
        if security_monitor.is_blocked(ip_address):
            security_monitor.log_security_event(
                '阻止访问',
                f'IP {ip_address} 尝试访问 {request.path}',
                ip_address,
                'WARNING'
            )
            from flask import abort
            abort(429)  # Too Many Requests

        # 检查SSL握手
        raw_uri = request.environ.get('RAW_URI', '')
        if security_monitor.is_ssl_handshake_request(raw_uri):
            security_monitor.log_security_event(
                'SSL握手检测',
                f'检测到SSL握手请求: {repr(raw_uri[:50])}',
                ip_address,
                'INFO'
            )
            from flask import abort
            abort(400)

        # 检查可疑请求
        suspicious_indicators = security_monitor.is_suspicious_request(request)
        if suspicious_indicators:
            security_monitor.log_security_event(
                '可疑请求',
                f'路径: {request.path}, 指标: {", ".join(suspicious_indicators)}',
                ip_address,
                'WARNING'
            )

        # 检查访问频率
        if not security_monitor.check_rate_limit(ip_address):
            from flask import abort
            abort(429)
    
    return app
