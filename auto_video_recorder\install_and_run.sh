#!/bin/bash

# StudentsCMSSP 自动化视频录制器 - 一键安装运行脚本 (Linux/macOS)

set -e  # 遇到错误立即退出

echo "================================================================"
echo "StudentsCMSSP 自动化视频录制器 - 一键安装运行"
echo "================================================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo "✅ Python已安装"
python3 --version

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "安装pip..."
    curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
    python3 get-pip.py
    rm get-pip.py
fi

# 检查StudentsCMSSP是否运行
echo
echo "检查StudentsCMSSP系统状态..."
if curl -s http://127.0.0.1:8080 > /dev/null 2>&1; then
    echo "✅ StudentsCMSSP系统运行正常"
else
    echo "❌ StudentsCMSSP系统未运行"
    echo "请先启动系统，然后重新运行此脚本"
    echo
    echo "启动命令:"
    echo "cd /path/to/StudentsCMSSP"
    echo "python3 run.py"
    echo
    exit 1
fi

# 创建必要目录
echo
echo "创建目录结构..."
mkdir -p recordings/{raw_videos,audio,screenshots}
mkdir -p output/{temp,final}
mkdir -p logs
echo "✅ 目录创建完成"

# 安装系统依赖 (Ubuntu/Debian)
if command -v apt-get &> /dev/null; then
    echo
    echo "安装系统依赖..."
    sudo apt-get update
    sudo apt-get install -y ffmpeg xvfb python3-dev
    echo "✅ 系统依赖安装完成"
fi

# 安装系统依赖 (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo
    echo "安装系统依赖..."
    if command -v brew &> /dev/null; then
        brew install ffmpeg
        echo "✅ 系统依赖安装完成"
    else
        echo "⚠️  请先安装Homebrew: https://brew.sh/"
    fi
fi

# 安装Python依赖
echo
echo "安装Python依赖..."
python3 install_fix.py

# 运行录制器
echo
echo "================================================================"
echo "准备开始录制..."
echo "================================================================"
echo
echo "选择运行模式:"
echo "1. 快速演示 (推荐新手)"
echo "2. 完整录制 (需要完整配置)"
echo
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo
        echo "启动快速演示模式..."
        echo "注意: 录制过程中请勿操作鼠标和键盘"
        echo
        read -p "按回车键开始录制..."
        python3 quick_start.py
        ;;
    2)
        echo
        echo "启动完整录制模式..."
        echo "请确保已配置 config/recording_config.json"
        echo
        read -p "按回车键开始录制..."
        python3 main.py
        ;;
    *)
        echo "无效选择，默认使用快速演示模式"
        echo
        read -p "按回车键开始录制..."
        python3 quick_start.py
        ;;
esac

echo
echo "================================================================"
echo "录制完成！"
echo "视频文件保存在 output 目录中"
echo "================================================================"
