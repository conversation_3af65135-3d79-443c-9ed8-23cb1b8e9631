# StudentsCMSSP 自动化视频录制器

一个基于Python的全自动视频录制解决方案，专为StudentsCMSSP学校食堂管理系统设计，支持从Markdown脚本到成品视频的完全自动化流程。

## 🌟 主要特性

- **完全自动化**: 从脚本解析到视频输出的全自动流程
- **智能浏览器控制**: 基于Playwright的精确浏览器操作
- **专业音频合成**: 支持Azure TTS、Google TTS和本地TTS
- **高质量录制**: 1080p高清录制，30fps流畅播放
- **灵活配置**: 丰富的配置选项，满足不同需求
- **错误恢复**: 智能错误处理和重试机制
- **跨平台支持**: Windows、macOS、Linux全平台支持

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- FFmpeg
- 4GB+ 内存
- 稳定的网络连接

### 2. 安装

```bash
# 克隆项目
git clone https://github.com/your-repo/auto_video_recorder.git
cd auto_video_recorder

# 运行安装脚本
python setup.py

# 或手动安装
pip install -r requirements.txt
playwright install chromium
```

### 3. 配置

编辑 `config/recording_config.json` 文件：

```json
{
  "script_file": "食谱管理模块视频录制脚本.md",
  "tts": {
    "engine": "azure",
    "azure_key": "your_azure_key",
    "azure_region": "eastasia"
  }
}
```

### 4. 运行

```bash
python main.py
```

## 📁 项目结构

```
auto_video_recorder/
├── config/                 # 配置文件
│   └── recording_config.json
├── scripts/                # 脚本解析器
│   └── script_parser.py
├── recorder/               # 录制模块
│   ├── browser_controller.py
│   ├── audio_generator.py
│   └── screen_recorder.py
├── editor/                 # 视频编辑
│   └── video_editor.py
├── utils/                  # 工具模块
│   ├── logger.py
│   └── file_manager.py
├── recordings/             # 录制文件
│   ├── raw_videos/
│   ├── audio/
│   └── screenshots/
├── output/                 # 输出文件
│   ├── temp/
│   └── final/
└── main.py                # 主程序
```

## 🎬 录制脚本格式

录制脚本使用Markdown格式，支持以下结构：

```markdown
## 场景标题 (开始时间-结束时间)

### 访问路径
`http://127.0.0.1:8080/recipe/`

### 解说词
"这里是解说词内容..."

### 录制步骤
1. **操作标题**
   ```
   操作步骤:
   1. 点击"添加食谱"按钮
   2. 填写食谱名称: 红烧肉
   3. 选择分类: 荤菜
   ```

### 技术亮点解说
"技术特色说明..."
```

## ⚙️ 配置说明

### TTS配置

支持多种TTS引擎：

```json
{
  "tts": {
    "engine": "azure",           // azure, google, local
    "azure_key": "your_key",
    "azure_region": "eastasia",
    "voice": "zh-CN-XiaoxiaoNeural",
    "rate": "0.9",
    "pitch": "medium"
  }
}
```

### 视频配置

```json
{
  "video": {
    "fps": 30,
    "quality": "high",
    "resolution": "1920x1080",
    "bitrate": "5000k",
    "codec": "libx264"
  }
}
```

### 录制配置

```json
{
  "recording": {
    "fps": 30,
    "cursor_enabled": true,
    "highlight_clicks": true,
    "audio_enabled": false
  }
}
```

## 🔧 高级功能

### 1. 自定义操作

支持自定义浏览器操作：

```python
async def custom_action(self, step):
    # 自定义操作逻辑
    pass
```

### 2. 错误处理

```json
{
  "error_handling": {
    "retry_attempts": 3,
    "retry_delay": 2,
    "continue_on_error": true,
    "screenshot_on_error": true
  }
}
```

### 3. 性能优化

```json
{
  "performance": {
    "parallel_processing": true,
    "max_workers": 4,
    "memory_limit": "4GB"
  }
}
```

## 🎯 使用场景

### 1. 产品演示视频

- 自动化产品功能演示
- 标准化操作流程录制
- 多语言版本制作

### 2. 培训教程

- 员工培训视频制作
- 操作手册视频化
- 标准化培训内容

### 3. 测试回归

- 自动化测试录制
- 功能回归验证
- 问题复现记录

## 🛠️ 开发指南

### 添加新的TTS引擎

```python
class CustomTTSEngine:
    def generate_audio(self, text, output_file):
        # 实现TTS逻辑
        pass
```

### 扩展浏览器操作

```python
async def new_action_type(self, step):
    # 实现新的操作类型
    pass
```

### 自定义视频效果

```python
def add_custom_effect(self, clip):
    # 添加自定义视频效果
    return clip
```

## 📊 性能指标

- **录制质量**: 1080p@30fps
- **音频质量**: 48kHz立体声
- **处理速度**: 实时录制，2x速度后期处理
- **文件大小**: 约100MB/分钟（高质量）
- **内存占用**: 2-4GB（取决于视频长度）

## 🐛 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   playwright install chromium
   ```

2. **音频生成失败**
   - 检查TTS配置
   - 验证API密钥
   - 尝试备用引擎

3. **视频合成失败**
   - 检查FFmpeg安装
   - 验证磁盘空间
   - 检查文件权限

### 日志分析

查看详细日志：
```bash
tail -f logs/recording.log
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 技术支持

- 邮箱: <EMAIL>
- 电话: 18373062333
- 官网: https://www.studentscms.com

## 🔄 更新日志

### v1.0.0 (2025-06-23)
- 初始版本发布
- 支持基础录制功能
- 集成Azure TTS
- 完整的视频后期处理

### 计划功能
- [ ] 实时预览功能
- [ ] 云端录制支持
- [ ] 多语言字幕
- [ ] AI智能剪辑
- [ ] 批量录制模式

---

**StudentsCMSSP 自动化视频录制器** - 让视频制作变得简单高效！
