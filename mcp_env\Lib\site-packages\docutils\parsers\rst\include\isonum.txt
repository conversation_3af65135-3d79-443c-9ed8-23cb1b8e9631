.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |amp|    unicode:: U+00026 .. AMPERSAND
.. |apos|   unicode:: U+00027 .. APOSTROPHE
.. |ast|    unicode:: U+0002A .. ASTERISK
.. |brvbar| unicode:: U+000A6 .. BROKEN BAR
.. |bsol|   unicode:: U+0005C .. REVERSE SOLIDUS
.. |cent|   unicode:: U+000A2 .. CENT SIGN
.. |colon|  unicode:: U+0003A .. COLON
.. |comma|  unicode:: U+0002C .. COMMA
.. |commat| unicode:: U+00040 .. COMMERCIAL AT
.. |copy|   unicode:: U+000A9 .. COPYRIGHT SIGN
.. |curren| unicode:: U+000A4 .. CURRENCY SIGN
.. |darr|   unicode:: U+02193 .. DOWNWARDS ARROW
.. |deg|    unicode:: U+000B0 .. DEGREE SIGN
.. |divide| unicode:: U+000F7 .. DIVISION SIGN
.. |dollar| unicode:: U+00024 .. DOLLAR SIGN
.. |equals| unicode:: U+0003D .. EQUALS SIGN
.. |excl|   unicode:: U+00021 .. EXCLAMATION MARK
.. |frac12| unicode:: U+000BD .. VULGAR FRACTION ONE HALF
.. |frac14| unicode:: U+000BC .. VULGAR FRACTION ONE QUARTER
.. |frac18| unicode:: U+0215B .. VULGAR FRACTION ONE EIGHTH
.. |frac34| unicode:: U+000BE .. VULGAR FRACTION THREE QUARTERS
.. |frac38| unicode:: U+0215C .. VULGAR FRACTION THREE EIGHTHS
.. |frac58| unicode:: U+0215D .. VULGAR FRACTION FIVE EIGHTHS
.. |frac78| unicode:: U+0215E .. VULGAR FRACTION SEVEN EIGHTHS
.. |gt|     unicode:: U+0003E .. GREATER-THAN SIGN
.. |half|   unicode:: U+000BD .. VULGAR FRACTION ONE HALF
.. |horbar| unicode:: U+02015 .. HORIZONTAL BAR
.. |hyphen| unicode:: U+02010 .. HYPHEN
.. |iexcl|  unicode:: U+000A1 .. INVERTED EXCLAMATION MARK
.. |iquest| unicode:: U+000BF .. INVERTED QUESTION MARK
.. |laquo|  unicode:: U+000AB .. LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
.. |larr|   unicode:: U+02190 .. LEFTWARDS ARROW
.. |lcub|   unicode:: U+0007B .. LEFT CURLY BRACKET
.. |ldquo|  unicode:: U+0201C .. LEFT DOUBLE QUOTATION MARK
.. |lowbar| unicode:: U+0005F .. LOW LINE
.. |lpar|   unicode:: U+00028 .. LEFT PARENTHESIS
.. |lsqb|   unicode:: U+0005B .. LEFT SQUARE BRACKET
.. |lsquo|  unicode:: U+02018 .. LEFT SINGLE QUOTATION MARK
.. |lt|     unicode:: U+0003C .. LESS-THAN SIGN
.. |micro|  unicode:: U+000B5 .. MICRO SIGN
.. |middot| unicode:: U+000B7 .. MIDDLE DOT
.. |nbsp|   unicode:: U+000A0 .. NO-BREAK SPACE
.. |not|    unicode:: U+000AC .. NOT SIGN
.. |num|    unicode:: U+00023 .. NUMBER SIGN
.. |ohm|    unicode:: U+02126 .. OHM SIGN
.. |ordf|   unicode:: U+000AA .. FEMININE ORDINAL INDICATOR
.. |ordm|   unicode:: U+000BA .. MASCULINE ORDINAL INDICATOR
.. |para|   unicode:: U+000B6 .. PILCROW SIGN
.. |percnt| unicode:: U+00025 .. PERCENT SIGN
.. |period| unicode:: U+0002E .. FULL STOP
.. |plus|   unicode:: U+0002B .. PLUS SIGN
.. |plusmn| unicode:: U+000B1 .. PLUS-MINUS SIGN
.. |pound|  unicode:: U+000A3 .. POUND SIGN
.. |quest|  unicode:: U+0003F .. QUESTION MARK
.. |quot|   unicode:: U+00022 .. QUOTATION MARK
.. |raquo|  unicode:: U+000BB .. RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
.. |rarr|   unicode:: U+02192 .. RIGHTWARDS ARROW
.. |rcub|   unicode:: U+0007D .. RIGHT CURLY BRACKET
.. |rdquo|  unicode:: U+0201D .. RIGHT DOUBLE QUOTATION MARK
.. |reg|    unicode:: U+000AE .. REGISTERED SIGN
.. |rpar|   unicode:: U+00029 .. RIGHT PARENTHESIS
.. |rsqb|   unicode:: U+0005D .. RIGHT SQUARE BRACKET
.. |rsquo|  unicode:: U+02019 .. RIGHT SINGLE QUOTATION MARK
.. |sect|   unicode:: U+000A7 .. SECTION SIGN
.. |semi|   unicode:: U+0003B .. SEMICOLON
.. |shy|    unicode:: U+000AD .. SOFT HYPHEN
.. |sol|    unicode:: U+0002F .. SOLIDUS
.. |sung|   unicode:: U+0266A .. EIGHTH NOTE
.. |sup1|   unicode:: U+000B9 .. SUPERSCRIPT ONE
.. |sup2|   unicode:: U+000B2 .. SUPERSCRIPT TWO
.. |sup3|   unicode:: U+000B3 .. SUPERSCRIPT THREE
.. |times|  unicode:: U+000D7 .. MULTIPLICATION SIGN
.. |trade|  unicode:: U+02122 .. TRADE MARK SIGN
.. |uarr|   unicode:: U+02191 .. UPWARDS ARROW
.. |verbar| unicode:: U+0007C .. VERTICAL LINE
.. |yen|    unicode:: U+000A5 .. YEN SIGN
