"""
浏览器控制器
使用Playwright控制浏览器执行录制脚本中的操作
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from playwright.async_api import async_playwright, Page, Browser, BrowserContext

class BrowserController:
    """浏览器控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger('BrowserController')
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
    
    async def initialize(self):
        """初始化浏览器"""
        self.logger.info("初始化浏览器...")
        
        self.playwright = await async_playwright().start()
        
        # 启动浏览器
        self.browser = await self.playwright.chromium.launch(
            headless=self.config.get('headless', False),
            slow_mo=self.config.get('slow_mo', 1000),
            args=[
                '--start-maximized',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        )
        
        # 创建浏览器上下文
        viewport = self.config.get('viewport', {'width': 1920, 'height': 1080})
        self.context = await self.browser.new_context(
            viewport=viewport,
            ignore_https_errors=True,
            java_script_enabled=True
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        # 设置默认超时
        self.page.set_default_timeout(30000)
        
        # 监听控制台消息
        self.page.on('console', self._handle_console_message)
        
        self.logger.info("浏览器初始化完成")
    
    async def execute_scene(self, scene: Dict[str, Any]):
        """执行场景录制"""
        self.logger.info(f"执行场景: {scene['title']}")
        
        try:
            # 导航到指定URL
            if scene.get('url'):
                await self._navigate_to_url(scene['url'])
            
            # 执行操作步骤
            for action in scene.get('actions', []):
                await self._execute_action(action)
            
            # 等待场景完成
            await asyncio.sleep(2)
            
        except Exception as e:
            self.logger.error(f"执行场景失败: {e}")
            raise
    
    async def _navigate_to_url(self, url: str):
        """导航到指定URL"""
        self.logger.info(f"导航到: {url}")
        
        try:
            await self.page.goto(url, wait_until='networkidle')
            await asyncio.sleep(2)  # 等待页面稳定
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            raise
    
    async def _execute_action(self, action: Dict[str, Any]):
        """执行操作"""
        self.logger.info(f"执行操作: {action['title']}")
        
        try:
            for step in action.get('steps', []):
                await self._execute_step(step)
                
                # 步骤间等待
                wait_time = step.get('wait_time', 1.0)
                await asyncio.sleep(wait_time)
                
        except Exception as e:
            self.logger.error(f"执行操作失败: {e}")
            # 继续执行下一个操作，不中断整个流程
    
    async def _execute_step(self, step: Dict[str, Any]):
        """执行单个步骤"""
        action_type = step['action']
        description = step['description']
        target = step.get('target')
        value = step.get('value')
        
        self.logger.debug(f"执行步骤: {description}")
        
        try:
            if action_type == 'click':
                await self._click_element(target, description)
            elif action_type == 'type':
                await self._type_text(target, value, description)
            elif action_type == 'select':
                await self._select_option(target, value, description)
            elif action_type == 'wait':
                await self._wait(step.get('wait_time', 1.0))
            elif action_type == 'display':
                await self._highlight_element(target, description)
            elif action_type == 'navigate':
                if target:
                    await self._navigate_to_url(target)
            elif action_type == 'scroll':
                await self._scroll_page(target, description)
            elif action_type == 'hover':
                await self._hover_element(target, description)
            else:
                self.logger.warning(f"未知操作类型: {action_type}")
                
        except Exception as e:
            self.logger.error(f"执行步骤失败: {description}, 错误: {e}")
    
    async def _click_element(self, target: str, description: str):
        """点击元素"""
        if not target:
            # 尝试从描述中提取目标
            target = self._extract_target_from_description(description)
        
        if target:
            try:
                # 等待元素可见
                await self.page.wait_for_selector(target, timeout=10000)
                
                # 高亮元素
                await self._highlight_element(target, "点击目标")
                
                # 点击元素
                await self.page.click(target)
                
            except Exception as e:
                # 尝试通过文本查找并点击
                await self._click_by_text(description)
        else:
            await self._click_by_text(description)
    
    async def _click_by_text(self, description: str):
        """通过文本内容点击元素"""
        # 提取可能的按钮文本
        text_patterns = [
            r'"(.+?)"',
            r'点击(.+?)按钮',
            r'点击(.+)',
            r'选择(.+)'
        ]
        
        import re
        for pattern in text_patterns:
            match = re.search(pattern, description)
            if match:
                text = match.group(1).strip()
                try:
                    # 尝试点击包含该文本的元素
                    await self.page.click(f'text="{text}"')
                    return
                except:
                    continue
        
        self.logger.warning(f"无法找到点击目标: {description}")
    
    async def _type_text(self, target: str, value: str, description: str):
        """输入文本"""
        if not target:
            target = self._extract_target_from_description(description)
        
        if not value:
            value = self._extract_value_from_description(description)
        
        if target and value:
            try:
                await self.page.wait_for_selector(target, timeout=10000)
                await self._highlight_element(target, "输入目标")
                
                # 清空输入框
                await self.page.fill(target, '')
                
                # 逐字输入（模拟真实输入）
                await self.page.type(target, value, delay=100)
                
            except Exception as e:
                self.logger.error(f"输入文本失败: {e}")
        else:
            self.logger.warning(f"缺少输入目标或值: {description}")
    
    async def _select_option(self, target: str, value: str, description: str):
        """选择选项"""
        if not target:
            target = self._extract_target_from_description(description)
        
        if not value:
            value = self._extract_value_from_description(description)
        
        if target:
            try:
                await self.page.wait_for_selector(target, timeout=10000)
                await self._highlight_element(target, "选择目标")
                
                if value:
                    await self.page.select_option(target, value)
                else:
                    # 选择第一个非空选项
                    options = await self.page.query_selector_all(f'{target} option')
                    if len(options) > 1:
                        await self.page.select_option(target, index=1)
                        
            except Exception as e:
                self.logger.error(f"选择选项失败: {e}")
    
    async def _wait(self, seconds: float):
        """等待指定时间"""
        self.logger.debug(f"等待 {seconds} 秒")
        await asyncio.sleep(seconds)
    
    async def _highlight_element(self, target: str, description: str):
        """高亮显示元素"""
        if not target:
            return
        
        try:
            await self.page.evaluate(f"""
                const element = document.querySelector('{target}');
                if (element) {{
                    const originalStyle = element.style.cssText;
                    element.style.border = '3px solid #ff0000';
                    element.style.backgroundColor = '#ffff00';
                    element.style.boxShadow = '0 0 10px #ff0000';
                    
                    setTimeout(() => {{
                        element.style.cssText = originalStyle;
                    }}, 2000);
                }}
            """)
            
            await asyncio.sleep(2)
            
        except Exception as e:
            self.logger.debug(f"高亮元素失败: {e}")
    
    async def _scroll_page(self, target: str, description: str):
        """滚动页面"""
        try:
            if target:
                # 滚动到指定元素
                await self.page.scroll_into_view_if_needed(target)
            else:
                # 滚动页面
                if '向下' in description or 'down' in description.lower():
                    await self.page.keyboard.press('PageDown')
                elif '向上' in description or 'up' in description.lower():
                    await self.page.keyboard.press('PageUp')
                else:
                    await self.page.keyboard.press('PageDown')
                    
        except Exception as e:
            self.logger.error(f"滚动失败: {e}")
    
    async def _hover_element(self, target: str, description: str):
        """悬停元素"""
        if not target:
            target = self._extract_target_from_description(description)
        
        if target:
            try:
                await self.page.wait_for_selector(target, timeout=10000)
                await self.page.hover(target)
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"悬停失败: {e}")
    
    def _extract_target_from_description(self, description: str) -> Optional[str]:
        """从描述中提取目标选择器"""
        import re
        
        # 常见的选择器模式
        patterns = [
            r'#([a-zA-Z][\w-]*)',  # ID选择器
            r'\.([a-zA-Z][\w-]*)',  # 类选择器
            r'\[(.+?)\]',  # 属性选择器
            r'`(.+?)`'   # 反引号内的选择器
        ]
        
        for pattern in patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_value_from_description(self, description: str) -> Optional[str]:
        """从描述中提取值"""
        import re
        
        patterns = [
            r'输入[：:](.+?)(?=\n|，|。|$)',
            r'填写[：:](.+?)(?=\n|，|。|$)',
            r'"(.+?)"'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type == 'error':
            self.logger.warning(f"页面错误: {msg.text}")
    
    async def take_screenshot(self, filename: str):
        """截取屏幕截图"""
        try:
            await self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"截图保存: {filename}")
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            self.logger.info("浏览器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {e}")

if __name__ == "__main__":
    # 测试浏览器控制器
    import json
    
    config = {
        'headless': False,
        'slow_mo': 1000,
        'viewport': {'width': 1920, 'height': 1080}
    }
    
    async def test():
        controller = BrowserController(config)
        await controller.initialize()
        
        # 测试导航
        await controller._navigate_to_url('http://127.0.0.1:8080')
        
        # 等待
        await asyncio.sleep(5)
        
        # 关闭
        await controller.close()
    
    asyncio.run(test())
