#!/usr/bin/env python3
"""
临时脚本：解除localhost的阻止状态
"""

from app import create_app
from app.security_monitor import security_monitor
from app.security_config import unblock_ip

def unblock_localhost():
    """解除localhost的阻止状态"""
    app = create_app()
    
    with app.app_context():
        # 解除security_monitor中的阻止
        security_monitor.unblock_ip('127.0.0.1')
        security_monitor.unblock_ip('localhost')
        security_monitor.unblock_ip('::1')
        
        # 解除security_config中的阻止
        unblock_ip('127.0.0.1')
        unblock_ip('localhost')
        unblock_ip('::1')
        
        print("已解除localhost的阻止状态")
        print(f"当前被阻止的IP: {list(security_monitor.blocked_ips)}")

if __name__ == '__main__':
    unblock_localhost()
