@echo off
chcp 65001 >nul
echo ================================================================
echo StudentsCMSSP 自动化视频录制器 - 一键安装运行
echo ================================================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 检查StudentsCMSSP是否运行
echo.
echo 检查StudentsCMSSP系统状态...
curl -s http://127.0.0.1:8080 >nul 2>&1
if errorlevel 1 (
    echo ❌ StudentsCMSSP系统未运行
    echo 请先启动系统，然后重新运行此脚本
    echo.
    echo 启动命令:
    echo cd c:\StudentsCMSSP
    echo python run.py
    echo.
    pause
    exit /b 1
)

echo ✅ StudentsCMSSP系统运行正常

:: 创建必要目录
echo.
echo 创建目录结构...
if not exist "recordings\raw_videos" mkdir "recordings\raw_videos"
if not exist "recordings\audio" mkdir "recordings\audio"
if not exist "output" mkdir "output"
if not exist "logs" mkdir "logs"
echo ✅ 目录创建完成

:: 安装依赖
echo.
echo 安装依赖包...
python install_fix.py
if errorlevel 1 (
    echo ❌ 安装失败，尝试手动安装...
    python -m pip install --upgrade pip
    python -m pip install playwright pyttsx3 requests
    python -m playwright install chromium
)

:: 运行录制器
echo.
echo ================================================================
echo 准备开始录制...
echo ================================================================
echo.
echo 选择运行模式:
echo 1. 快速演示 (推荐新手)
echo 2. 完整录制 (需要完整配置)
echo.
set /p choice="请输入选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 启动快速演示模式...
    echo 注意: 录制过程中请勿操作鼠标和键盘
    echo.
    pause
    python quick_start.py
) else if "%choice%"=="2" (
    echo.
    echo 启动完整录制模式...
    echo 请确保已配置 config/recording_config.json
    echo.
    pause
    python main.py
) else (
    echo 无效选择，默认使用快速演示模式
    echo.
    pause
    python quick_start.py
)

echo.
echo ================================================================
echo 录制完成！
echo 视频文件保存在 output 目录中
echo ================================================================
pause
