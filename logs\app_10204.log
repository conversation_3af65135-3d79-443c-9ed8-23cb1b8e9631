2025-06-23 10:08:56,914 INFO: 应用启动 - PID: 10204 [in C:\StudentsCMSSP\app\__init__.py:868]
2025-06-23 10:09:37,356 ERROR: Exception on /guest-login [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
pyodbc.OperationalError: ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\app\auth\routes.py", line 61, in guest_login
    guest_user = User.query.filter_by(username='guest_demo').first()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2728, in first
    return self.limit(1)._iter().first()  # type: ignore
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2226, in _execute_internal
    conn = self._connection_for_bind(bind)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2095, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
  File "<string>", line 2, in _connection_for_bind
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 1189, in _connection_for_bind
    conn = bind.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3276, in connect
    return self._connection_cls(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 148, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2440, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 146, in __init__
    self._dbapi_connection = engine.raw_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 3300, in raw_connection
    return self.pool.connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 1263, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 712, in checkout
    rec = pool._do_get()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 180, in _do_get
    self._dec_overflow()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 674, in __init__
    self.__connect()
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    pool.logger.debug("Error on connect(): %s", e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\pool\base.py", line 896, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\create.py", line 643, in connect
    return dialect.connect(*cargs, **cparams)
  File "C:\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 620, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
sqlalchemy.exc.OperationalError: (pyodbc.OperationalError) ('08001', '[08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]SQL Server 不存在或访问被拒绝 (17) (SQLDriverConnect); [08001] [Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionOpen (Connect()). (2)')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-23 10:09:54,491 ERROR: [安全监控] 2025-06-23 10:09:54 - 频率限制触发 | IP: 127.0.0.1 | IP 127.0.0.1 在1分钟内请求 60 次 [in C:\StudentsCMSSP\app\security_monitor.py:126]
2025-06-23 10:09:54,492 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,494 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/vendor/sweetalert2/sweetalert2-zh-CN.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,497 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,500 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/form-validation-zh-CN.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,502 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/i18n.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,502 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,503 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mock-api-handler.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,504 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,505 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/main.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,506 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/theme-switcher-simple.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,506 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,508 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,509 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/enhanced-image-uploader.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,510 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,512 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/auth-helper.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,512 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,513 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/file-upload-fix.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,515 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-table-cards.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,515 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,517 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,517 WARNING: [安全监控] 2025-06-23 10:09:54 - 阻止访问 | IP: 127.0.0.1 | IP 127.0.0.1 尝试访问 /static/js/mobile-enhancements.js [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-23 10:09:54,518 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
2025-06-23 10:09:54,519 WARNING: SECURITY EVENT - RATE_LIMIT: Rate limit exceeded from 127.0.0.1 [in C:\StudentsCMSSP\app\security_config.py:155]
